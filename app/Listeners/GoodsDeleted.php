<?php

namespace App\Listeners;

use App\Models\Carmis;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Events\GoodsDeleted as GoodsDeletedEvent;

class GoodsDeleted
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(GoodsDeletedEvent $event): void
    {
        Carmis::query()->where('goods_id', $event->goods->id)->delete();
    }
}
