<?php
/**
 * The file was created by <PERSON>si<PERSON>.
 *
 * <AUTHOR>
 * @copyright assimon<<EMAIL>>
 * @link      http://utf8.hk/
 */

namespace App\Service;


use App\Exceptions\RuleValidationException;
use App\Models\BaseModel;
use App\Models\Coupon;
use App\Models\Goods;
use App\Models\Carmis;
use App\Models\Order;
use App\Rules\SearchPwd;
use App\Rules\VerifyImg;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class OrderService
{

    /**
     * 商品服务层.
     */
    private \App\Service\GoodsService $goodsService;

    /**
     * 优惠码服务层
     */
    private \App\Service\CouponService $couponService;

    public function __construct()
    {
        $this->goodsService = app('Service\GoodsService');
        $this->couponService = app('Service\CouponService');
    }


    /**
     * 验证集合
     *
     * @param Request $request
     * @throws RuleValidationException
     * @throws \Illuminate\Validation\ValidationException
     *
     * <AUTHOR>
     * @copyright assimon<<EMAIL>>
     * @link      http://utf8.hk/
     */
 public function validatorCreateOrder(Request $request): void
{
    $rules = [
        'gid' => 'required',
        'payway' => ['required', 'integer'],
        'search_pwd' => [new SearchPwd()],
        'by_amount' => ['required', 'integer', 'min:1'],
        'img_verify_code' => [new VerifyImg()],
    ];

    $messages = [
        'by_amount.required' => __('dujiaoka.prompt.buy_amount_format_error'),
        'by_amount.integer' => __('dujiaoka.prompt.buy_amount_format_error'),
        'by_amount.min' => __('dujiaoka.prompt.buy_amount_format_error'),
        'payway.required' => __('dujiaoka.prompt.please_select_mode_of_payment'),
        'payway.integer' => __('dujiaoka.prompt.please_select_mode_of_payment'),
        'gid.required' => __('dujiaoka.prompt.goods_does_not_exist'),
    ];

    // Check if email format validation is required based on the custom switch
    if (dujiaoka_config_get('is_open_mail') == BaseModel::STATUS_OPEN){
          $rules['email'] = 'required';
      
        
    } else {
        $rules['email'] = 'required|email';
        $messages['email.required'] = __('dujiaoka.prompt.email_format_error');
        $messages['email.email'] = __('dujiaoka.prompt.email_format_error');
    }

    $validator = Validator::make($request->all(), $rules, $messages);

    if ($validator->fails()) {
        throw new RuleValidationException($validator->errors()->first());
    }

    // 极验验证
    if (
        dujiaoka_config_get('is_open_geetest') == BaseModel::STATUS_OPEN
        &&
        !Validator::make($request->all(),
            ['geetest_challenge' => 'geetest',],
            ['geetest' => __('dujiaoka.prompt.geetest_validate_fail')])
    ) {
        throw new RuleValidationException(__('dujiaoka.prompt.geetest_validate_fail'));
    }


   
    // 待支付订单限制
        $limit = dujiaoka_config_get('order_ip_limits');
        if($limit > 0){
            $count = Order::where('buy_ip', $request->getClientIp())
            ->where('status', Order::STATUS_WAIT_PAY)
            ->limit($limit)
            ->count();
            
            if ($count >= $limit)
                throw new RuleValidationException(__('休息下，您还有很多订单没有支付呢！'));
        }
    }
    /**
     * 得到商品详情并验证
     *
     * @param Request $request 请求
     * @throws RuleValidationException
     *
     * <AUTHOR>
     * @copyright assimon<<EMAIL>>
     * @link      http://utf8.hk/
     */
    public function validatorGoods(Request $request): Goods
    {
        // 获得商品详情
        $goods = $this->goodsService->detail($request->input('gid'));
        // 商品状态验证
        $this->goodsService->validatorGoodsStatus($goods);
        // 如果有限购
        if ($goods->buy_limit_num > 0 && $request->input('by_amount') > $goods->buy_limit_num) {
            throw new RuleValidationException(__('dujiaoka.prompt.purchase_limit_exceeded'));
        }
        
        // 如果有最低购买限制
       if ($goods->min_buy_num > 0 && $request->input('by_amount') < $goods->min_buy_num) {
    $actualAmount = $request->input('by_amount');
    $minRequiredAmount = $goods->min_buy_num;
    throw new RuleValidationException(__("当前购买数量：:actualAmount，最低购买数量：:minRequiredAmount", ['actualAmount' => $actualAmount, 'minRequiredAmount' => $minRequiredAmount]));
}

      // 库存不足
        if ($request->input('by_amount') > $goods->in_stock) {
            throw new RuleValidationException(__('dujiaoka.prompt.inventory_shortage'));
        }
        // 预选，且预选卡密不存在
        if($request->input('carmi_id')){
            if(!$this->goodsService->checkCarmiBelong($goods['id'], $request->input('carmi_id'))){
                throw new RuleValidationException(__('dujiaoka.prompt.preselect_unable'));
            }
        }
        return $goods;
    }
    /**
     * 判断是否有循环卡密
     *
     * @param int $goodsID 商品id
     * @return array|null
     *
     * <AUTHOR>
     * @copyright ZhangYiQiu<<EMAIL>>
     * @link      http://zhangyiqiu.net/
     */
    public function validatorLoopCarmis(Request $request)
    {
        $carmis = Carmis::query()
            ->where('goods_id', $request->input('gid'))
            ->where('status', Carmis::STATUS_UNSOLD)
            ->where('is_loop', true)
            ->count();
        if($carmis > 0 && $request->input('by_amount') > 1){
			throw new RuleValidationException(__('dujiaoka.prompt.loop_carmis_limit'));
		}
		return $carmis;
    }

    /**
     * 优惠码验证
     *
     * @param Request $request
     * @return Coupon|null
     * @throws RuleValidationException
     *
     * <AUTHOR>
     * @copyright assimon<<EMAIL>>
     * @link      http://utf8.hk/
     */
    public function validatorCoupon(Request $request):? Coupon
    {
        // 如果提交了优惠码
        if ($request->filled('coupon_code')) {
            // 查询优惠码是否存在
            $coupon = $this->couponService->withHasGoods($request->input('coupon_code'), $request->input('gid'));
            // 此商品没有这个优惠码
            if (empty($coupon)) {
                throw new RuleValidationException(__('dujiaoka.prompt.coupon_does_not_exist'));
            }
            // 剩余次数不足
            if ($coupon->ret <= 0) {
                throw new RuleValidationException(__('dujiaoka.prompt.coupon_lack_of_available_opportunities'));
            }
            return $coupon;
        }
        return null;
    }

    /**
     * 代充框验证.
     *
     * @param Goods $goods
     * @param Request $request
     * @return string
     * @throws RuleValidationException
     *
     * <AUTHOR>
     * @copyright assimon<<EMAIL>>
     * @link      http://utf8.hk/
     */
    public function validatorChargeInput(Goods $goods, Request $request): string
    {
        $otherIpt = '';
        // 代充框验证
        if ($goods->type == Goods::MANUAL_PROCESSING && !empty($goods->other_ipu_cnf)) {
            // 如果有其他输入框 判断其他输入框内容  然后载入信息
            $formatIpt = format_charge_input($goods->other_ipu_cnf);
            foreach ($formatIpt as $item) {
                if ($item['rule'] && !$request->filled($item['field'])) {
                    $errMessage = $item['desc'] . __('dujiaoka.prompt.can_not_be_empty');
                    throw new RuleValidationException($errMessage);
                }
                $otherIpt .= $item['desc'].':'.$request->input($item['field']) . PHP_EOL;
            }
        }
        return $otherIpt;
    }

    /**
     * 通过订单号查询订单
     * @param string $orderSN
     * @return Order
     *
     * <AUTHOR>
     * @copyright assimon<<EMAIL>>
     * @link      http://utf8.hk/
     */
    public function detailOrderSN(string $orderSN):? Order
    {
        $order = Order::query()->with(['coupon', 'pay', 'goods'])->where('order_sn', $orderSN)->first();
        return $order;
    }

    /**
     * 根据订单号过期订单.
     *
     * @param string $orderSN
     * @return bool
     *
     * <AUTHOR>
     * @copyright assimon<<EMAIL>>
     * @link      http://utf8.hk/
     */
    public function expiredOrderSN(string $orderSN): bool
    {
        return Order::query()->where('order_sn', $orderSN)->update(['status' => Order::STATUS_EXPIRED]);
    }

  

    /**
     * 通过邮箱和查询密码查询
     *
     * @param string $email 邮箱
     * @param string $searchPwd 查询面面
     * @return array|\Illuminate\Database\Concerns\BuildsQueries[]|\Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Query\Builder[]|\Illuminate\Support\Collection
     *
     * <AUTHOR>
     * @copyright assimon<<EMAIL>>
     * @link      http://utf8.hk/
     */
    public function withEmailAndPassword(string $email, string $searchPwd = '')
    {
        return Order::query()
            ->where('email', $email)
            ->when(!empty($searchPwd), function ($query) use ($searchPwd) {
                $query->where('search_pwd', $searchPwd);
            })
            ->orderBy('created_at', 'DESC')
            ->take(5)
            ->get();
    }

    /**
     * 通过订单号集合查询
     *
     * @param array $orderSNS 订单号集合
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Database\Query\Builder[]|\Illuminate\Support\Collection
     *
     * <AUTHOR>
     * @copyright assimon<<EMAIL>>
     * @link      http://utf8.hk/
     */
    public function byOrderSNS(array $orderSNS)
    {
        return Order::query()
            ->whereIn('order_sn', $orderSNS)
            ->orderBy('created_at', 'DESC')
            ->take(5)
            ->get();
    }

}
