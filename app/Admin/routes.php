<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;
use App\Admin\Controllers\HomeController;
use App\Admin\Controllers\GoodsController;
use App\Admin\Controllers\GoodsGroupController;
use App\Admin\Controllers\CarmisController;
use App\Admin\Controllers\CouponController;
use App\Admin\Controllers\EmailtplController;
use App\Admin\Controllers\PayController;
use App\Admin\Controllers\OrderController;
use App\Admin\Controllers\UserController;
use App\Admin\Controllers\InviteUserController;
use App\Admin\Controllers\WithdrawController;
use App\Admin\Controllers\SystemSettingController;
use App\Admin\Controllers\EmailTestController;
use App\Admin\Controllers\ArticleController;
use App\Admin\Controllers\ArticleCategoryController;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {
    $router->get('/', [HomeController::class, 'index']);
    $router->resource('goods', GoodsController::class);
    $router->resource('goods-group', GoodsGroupController::class);
    $router->resource('carmis', CarmisController::class);
    $router->resource('coupon', CouponController::class);
    $router->resource('emailtpl', EmailtplController::class);
    $router->resource('pay', PayController::class);
    $router->resource('order', OrderController::class);
    $router->resource('user', UserController::class);
    $router->resource('invite', InviteUserController::class);
    $router->resource('withdraw', WithdrawController::class);
    $router->get('import-carmis', [CarmisController::class, 'importCarmis']);
    $router->get('system-setting', [SystemSettingController::class, 'systemSetting']);
    $router->get('email-test', [EmailTestController::class, 'emailTest']);
    $router->resource('article', ArticleController::class);
    $router->resource('article-category', ArticleCategoryController::class);
});
