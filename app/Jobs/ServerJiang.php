<?php

namespace App\Jobs;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ServerJiang implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务最大尝试次数。
     *
     * @var int
     */
    public $tries = 2;

    /**
     * 任务运行的超时时间。
     *
     * @var int
     */
    public $timeout = 30;

    /**
     * @var Order
     */
    private Order $order;

    /**
     * Create a new job instance.
     *
     * *
     */
    public function __construct(private Order $order)
    {
    }

    /**
     * Execute the job.
     *
     * *
     */
    public function handle(): void
    {
        $postdata = http_build_query([
            'text' => __('dujiaoka.prompt.new_order_push') . ":{$this->order['ord_title']}",
            'desp' => "
- ". __('order.fields.title') ."：{$this->order->title}
- ". __('order.fields.order_sn') ."：{$this->order->order_sn}
- ". __('order.fields.email') ."：{$this->order->email}
- ". __('order.fields.actual_price') ."：{$this->order->actual_price}
            "
        ]);
        $opts = [
            'http' => [
                'method'  => 'POST',
                'header'  => 'Content-type: application/x-www-form-urlencoded',
                'content' => $postdata
            ]
        ];
        $context  = stream_context_create($opts);
        $apiToken = dujiaoka_config_get('server_jiang_token');
        file_get_contents('https://sctapi.ftqq.com/' . $apiToken . '.send', false, $context);
    }
}
