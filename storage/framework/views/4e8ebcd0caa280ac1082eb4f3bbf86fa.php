
<!-- 企业级导航栏 -->
<nav class="navbar navbar-expand-lg navbar-glass fixed-top" id="mainNavbar">
    <div class="container">
        <!-- 品牌Logo -->
        <a class="navbar-brand d-flex align-items-center" href="/">
            <img src="<?php echo e(picture_url(dujiaoka_config_get('img_logo')), false); ?>"
                 alt="<?php echo e(dujiaoka_config_get('text_logo'), false); ?>"
                 class="me-2 glow-effect"
                 style="width: 40px; height: 40px; border-radius: 8px;">
            <span class="gradient-text fw-bold fs-4"><?php echo e(dujiaoka_config_get('text_logo'), false); ?></span>
        </a>

        <!-- 移动端菜单按钮 -->
        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <!-- 导航菜单 -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav mx-auto">
                <li class="nav-item">
                    <a class="nav-link text-white fw-semibold <?php echo e(Request::is('/') ? 'active' : '', false); ?>" href="/">
                        <i class="fas fa-home me-1"></i>首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white fw-semibold <?php echo e(Request::is('article*') ? 'active' : '', false); ?>" href="<?php echo e(url('/article'), false); ?>">
                        <i class="fas fa-newspaper me-1"></i>资讯
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white fw-semibold <?php echo e(Request::is('order-search*') ? 'active' : '', false); ?>" href="<?php echo e(url('/order-search'), false); ?>">
                        <i class="fas fa-search me-1"></i>订单查询
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white fw-semibold" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-headset me-1"></i>客服支持
                    </a>
                    <ul class="dropdown-menu glass-card border-0">
                        <li><a class="dropdown-item text-white" href="https://t.me/riniba" target="_blank">
                            <i class="fab fa-telegram me-2"></i>Telegram客服
                        </a></li>
                        <li><a class="dropdown-item text-white" href="mailto:<EMAIL>">
                            <i class="fas fa-envelope me-2"></i>邮件支持
                        </a></li>
                        <li><hr class="dropdown-divider" style="border-color: rgba(255,255,255,0.2);"></li>
                        <li><a class="dropdown-item text-white" href="#" data-bs-toggle="modal" data-bs-target="#helpModal">
                            <i class="fas fa-question-circle me-2"></i>帮助中心
                        </a></li>
                    </ul>
                </li>
            </ul>

            <!-- 右侧按钮组 -->
            <div class="d-flex align-items-center gap-3">
                <!-- 搜索按钮 -->
                <button class="btn btn-outline-light btn-sm" type="button" data-bs-toggle="modal" data-bs-target="#searchModal">
                    <i class="fas fa-search"></i>
                </button>

                <!-- 主题切换 -->
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-palette"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end glass-card border-0">
                        <li><button class="dropdown-item text-white" data-bs-theme-value="light">
                            <i class="fas fa-sun me-2"></i>浅色模式
                        </button></li>
                        <li><button class="dropdown-item text-white" data-bs-theme-value="dark">
                            <i class="fas fa-moon me-2"></i>深色模式
                        </button></li>
                        <li><button class="dropdown-item text-white" data-bs-theme-value="auto">
                            <i class="fas fa-circle-half-stroke me-2"></i>自动模式
                        </button></li>
                    </ul>
                </div>

                <!-- 用户菜单 -->
                <?php if(auth()->guard()->check()): ?>
                <div class="dropdown">
                    <button class="btn btn-gradient btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo e(Auth::user()->email, false); ?>

                    </button>
                    <ul class="dropdown-menu dropdown-menu-end glass-card border-0">
                        <li><a class="dropdown-item text-white" href="<?php echo e(url('/user'), false); ?>">
                            <i class="fas fa-user-circle me-2"></i>个人中心
                        </a></li>
                        <li><a class="dropdown-item text-white" href="<?php echo e(url('/user/invite'), false); ?>">
                            <i class="fas fa-gift me-2"></i>邀请奖励
                        </a></li>
                        <li><a class="dropdown-item text-white" href="<?php echo e(url('/user/wholesale'), false); ?>">
                            <i class="fas fa-shopping-cart me-2"></i>批发中心
                        </a></li>
                        <li><hr class="dropdown-divider" style="border-color: rgba(255,255,255,0.2);"></li>
                        <li><a class="dropdown-item text-white" href="<?php echo e(url('/logout'), false); ?>">
                            <i class="fas fa-sign-out-alt me-2"></i>退出登录
                        </a></li>
                    </ul>
                </div>
                <?php else: ?>
                <div class="d-flex gap-2">
                    <a href="<?php echo e(url('/login'), false); ?>" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-sign-in-alt me-1"></i>登录
                    </a>
                    <a href="<?php echo e(url('/register'), false); ?>" class="btn btn-gradient btn-sm">
                        <i class="fas fa-user-plus me-1"></i>注册
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</nav>

<!-- 搜索模态框 -->
<div class="modal fade" id="searchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content glass-card border-0">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white">
                    <i class="fas fa-search me-2"></i>搜索商品
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="position-relative">
                    <input type="text" class="form-control form-control-lg bg-transparent text-white border-light"
                           placeholder="输入商品名称或关键词..." id="globalSearchInput">
                    <button class="btn btn-gradient position-absolute top-50 end-0 translate-middle-y me-2" type="button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div id="searchResults" class="mt-4">
                    <!-- 搜索结果将在这里显示 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 帮助中心模态框 -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content glass-card border-0">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white">
                    <i class="fas fa-question-circle me-2"></i>帮助中心
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="glass-card p-3">
                            <h6 class="text-white mb-3">
                                <i class="fas fa-shopping-cart me-2"></i>购买流程
                            </h6>
                            <ol class="text-light small">
                                <li>选择商品并点击购买</li>
                                <li>填写必要信息</li>
                                <li>选择支付方式</li>
                                <li>完成支付</li>
                                <li>自动发货到邮箱</li>
                            </ol>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="glass-card p-3">
                            <h6 class="text-white mb-3">
                                <i class="fas fa-shield-alt me-2"></i>安全保障
                            </h6>
                            <ul class="text-light small">
                                <li>SSL加密传输</li>
                                <li>多重身份验证</li>
                                <li>7x24小时监控</li>
                                <li>资金安全保障</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="glass-card p-3">
                            <h6 class="text-white mb-3">
                                <i class="fas fa-clock me-2"></i>服务时间
                            </h6>
                            <p class="text-light small mb-0">
                                自动发货：7x24小时<br>
                                人工客服：9:00-23:00<br>
                                紧急支持：随时响应
                            </p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="glass-card p-3">
                            <h6 class="text-white mb-3">
                                <i class="fas fa-phone me-2"></i>联系方式
                            </h6>
                            <p class="text-light small mb-0">
                                Telegram: @riniba<br>
                                邮箱: <EMAIL><br>
                                QQ群: 123456789
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导航栏JavaScript增强 -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 导航栏滚动效果
    const navbar = document.getElementById('mainNavbar');
    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }

        // 向下滚动时隐藏导航栏，向上滚动时显示
        if (scrollTop > lastScrollTop && scrollTop > 200) {
            navbar.style.transform = 'translateY(-100%)';
        } else {
            navbar.style.transform = 'translateY(0)';
        }

        lastScrollTop = scrollTop;
    });

    // 全局搜索功能
    const globalSearchInput = document.getElementById('globalSearchInput');
    const searchResults = document.getElementById('searchResults');

    if (globalSearchInput) {
        globalSearchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length > 2) {
                performSearch(query);
            } else {
                searchResults.innerHTML = '';
            }
        });
    }

    function performSearch(query) {
        // 模拟搜索结果
        const mockResults = [
            { name: 'Steam礼品卡', price: '¥50.00', image: '/assets/images/steam.jpg' },
            { name: 'iTunes充值卡', price: '¥100.00', image: '/assets/images/itunes.jpg' },
            { name: 'Google Play卡', price: '¥25.00', image: '/assets/images/googleplay.jpg' }
        ];

        const filteredResults = mockResults.filter(item =>
            item.name.toLowerCase().includes(query.toLowerCase())
        );

        if (filteredResults.length > 0) {
            searchResults.innerHTML = `
                <h6 class="text-white mb-3">搜索结果 (${filteredResults.length})</h6>
                <div class="row g-3">
                    ${filteredResults.map(item => `
                        <div class="col-md-4">
                            <div class="glass-card p-3 text-center">
                                <img src="${item.image}" alt="${item.name}" class="mb-2" style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px;">
                                <h6 class="text-white mb-1">${item.name}</h6>
                                <p class="text-primary mb-2">${item.price}</p>
                                <button class="btn btn-gradient btn-sm w-100">查看详情</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        } else {
            searchResults.innerHTML = `
                <div class="text-center text-light">
                    <i class="fas fa-search fa-3x mb-3 opacity-50"></i>
                    <p>未找到相关商品</p>
                </div>
            `;
        }
    }

    // 活跃导航链接高亮
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    const currentPath = window.location.pathname;

    navLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
});
</script><?php /**PATH /www/wwwroot/www.bgoom.top/resources/views/neon/layouts/_nav.blade.php ENDPATH**/ ?>