<?php $__env->startSection('header'); ?>
<style>
/* 企业级主页专用样式 */
.hero-section {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    position: relative;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.stats-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.category-tab {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.category-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.category-tab:hover::before {
    left: 100%;
}

.category-tab.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.product-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover::before {
    opacity: 1;
}

.product-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

.product-image {
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

.price-tag {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
}

.stock-indicator {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 3;
}

.stock-badge {
    background: rgba(16, 185, 129, 0.9);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.stock-badge.out-of-stock {
    background: rgba(239, 68, 68, 0.9);
}

.progress-glow {
    background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.5);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.purchase-info {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    animation: slideInRight 0.5s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: floatUpDown 8s ease-in-out infinite;
}

@keyframes floatUpDown {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

@media (max-width: 768px) {
    .hero-section {
        min-height: 80vh;
    }

    .section-title {
        font-size: 2rem;
    }

    .product-card {
        margin-bottom: 1rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }
}
</style>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>

<!-- Hero Section -->
<section class="hero-section d-flex align-items-center">
    <!-- 浮动粒子背景 -->
    <div class="hero-particles">
        <div class="particle" style="left: 10%; top: 20%; width: 4px; height: 4px; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; top: 80%; width: 6px; height: 6px; animation-delay: 1s;"></div>
        <div class="particle" style="left: 60%; top: 30%; width: 3px; height: 3px; animation-delay: 2s;"></div>
        <div class="particle" style="left: 80%; top: 70%; width: 5px; height: 5px; animation-delay: 3s;"></div>
        <div class="particle" style="left: 30%; top: 10%; width: 4px; height: 4px; animation-delay: 4s;"></div>
        <div class="particle" style="left: 70%; top: 90%; width: 6px; height: 6px; animation-delay: 5s;"></div>
    </div>

    <div class="container hero-content">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6">
                <div class="text-white">
                    <h1 class="display-3 fw-bold mb-4 animate__animated animate__fadeInUp">
                        <?php echo e(dujiaoka_config_get('text_logo', '独角数卡'), false); ?>

                        <span class="gradient-text d-block">企业级数字商城</span>
                    </h1>
                    <p class="lead mb-5 animate__animated animate__fadeInUp animate__delay-1s">
                        <?php echo e(dujiaoka_config_get('seo_description', '专业的数字商品销售平台，提供安全、快速、可靠的购买体验'), false); ?>

                    </p>
                    <div class="d-flex flex-wrap gap-3 animate__animated animate__fadeInUp animate__delay-2s">
                        <a href="#products" class="btn btn-gradient btn-lg px-5 py-3">
                            <i class="fas fa-shopping-cart me-2"></i>立即购买
                        </a>
                        <a href="#features" class="btn btn-outline-light btn-lg px-5 py-3">
                            <i class="fas fa-info-circle me-2"></i>了解更多
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="row g-4 animate__animated animate__fadeInRight animate__delay-1s">
                    <!-- 统计卡片 -->
                    <div class="col-6">
                        <div class="stats-card p-4 text-center text-white">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3 class="h2 mb-2" id="userCount">10,000+</h3>
                            <p class="mb-0">活跃用户</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card p-4 text-center text-white">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <h3 class="h2 mb-2" id="orderCount">50,000+</h3>
                            <p class="mb-0">成功订单</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card p-4 text-center text-white">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h3 class="h2 mb-2">99.9%</h3>
                            <p class="mb-0">安全保障</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-card p-4 text-center text-white">
                            <div class="feature-icon mb-3">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h3 class="h2 mb-2">24/7</h3>
                            <p class="mb-0">在线服务</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 产品分类导航 -->
<section class="py-5" style="background: rgba(10, 14, 39, 0.8);">
    <div class="container">
        <h2 class="section-title text-white mb-5">商品分类</h2>

        <!-- 分类标签 -->
        <div class="row justify-content-center mb-5">
            <div class="col-12">
                <div class="d-flex flex-wrap justify-content-center gap-3" role="tablist">
                    <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <button class="category-tab px-4 py-3 border-0 text-white <?php echo e($index === 0 ? 'active' : '', false); ?>"
                            data-bs-toggle="tab"
                            data-bs-target="#group-<?php echo e($group['id'], false); ?>"
                            type="button"
                            role="tab">
                        <div class="d-flex align-items-center">
                            <img src="<?php echo e(category_picture_url($group['picture'] ?? ''), false); ?>"
                                 alt="<?php echo e($group['gp_name'], false); ?>"
                                 class="me-2"
                                 style="width: 32px; height: 32px; border-radius: 8px;">
                            <span class="fw-semibold"><?php echo e($group['gp_name'], false); ?></span>
                            <span class="badge bg-primary ms-2"><?php echo e(count($group['goods']), false); ?></span>
                        </div>
                    </button>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>

        <!-- 产品内容 -->
        <div class="tab-content" id="productsContent">
            <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="tab-pane fade <?php echo e($index === 0 ? 'show active' : '', false); ?>"
                 id="group-<?php echo e($group['id'], false); ?>"
                 role="tabpanel">
                <div class="row g-4">
                    <?php $__currentLoopData = $group['goods']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $goods): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="product-card p-4 h-100">
                            <!-- 库存指示器 -->
                            <div class="stock-indicator">
                                <?php if($goods['in_stock'] > 0): ?>
                                    <span class="stock-badge">
                                        <i class="fas fa-check-circle me-1"></i>有库存
                                    </span>
                                <?php else: ?>
                                    <span class="stock-badge out-of-stock">
                                        <i class="fas fa-times-circle me-1"></i>缺货
                                    </span>
                                <?php endif; ?>
                            </div>

                            <!-- 商品图片 -->
                            <div class="text-center mb-4">
                                <img src="<?php echo e(picture_url($goods['picture']), false); ?>"
                                     alt="<?php echo e($goods['gd_name'], false); ?>"
                                     class="product-image"
                                     style="width: 120px; height: 120px; object-fit: cover;">
                            </div>

                            <!-- 商品信息 -->
                            <div class="text-white text-center">
                                <h5 class="mb-3 fw-bold"><?php echo e($goods['gd_name'], false); ?></h5>
                                <p class="text-light mb-3" style="font-size: 0.9rem;">
                                    <?php echo e(Str::limit($goods['gd_description'], 80), false); ?>

                                </p>

                                <!-- 价格 -->
                                <div class="mb-4">
                                    <span class="price-tag h4 mb-0">
                                        ¥<?php echo e(number_format($goods['actual_price'], 2), false); ?>

                                    </span>
                                    <?php if($goods['original_price'] > $goods['actual_price']): ?>
                                        <span class="text-muted text-decoration-line-through ms-2">
                                            ¥<?php echo e(number_format($goods['original_price'], 2), false); ?>

                                        </span>
                                    <?php endif; ?>
                                </div>

                                <!-- 库存进度条 -->
                                <?php
                                    $maxStock = 50;
                                    $inStock = (int) $goods['in_stock'];
                                    $percent = 0;
                                    if($inStock > 0){
                                        $percent = ($inStock >= $maxStock) ? 100 : round(($inStock / $maxStock) * 100, 2);
                                    }
                                ?>
                                <div class="mb-4">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span class="small text-light">库存</span>
                                        <span class="small text-light"><?php echo e($inStock, false); ?></span>
                                    </div>
                                    <div class="progress" style="height: 8px; background: rgba(255,255,255,0.1);">
                                        <div class="progress-bar progress-glow"
                                             style="width: <?php echo e($percent, false); ?>%"></div>
                                    </div>
                                </div>

                                <!-- 购买按钮 -->
                                <?php if($goods['in_stock'] > 0): ?>
                                    <a href="<?php echo e(url("/buy/{$goods['id']}"), false); ?>"
                                       class="btn btn-gradient w-100 py-3">
                                        <i class="fas fa-shopping-cart me-2"></i>立即购买
                                    </a>
                                <?php else: ?>
                                    <button class="btn btn-secondary w-100 py-3" disabled>
                                        <i class="fas fa-times me-2"></i>暂时缺货
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<!-- 特色功能区 -->
<section id="features" class="py-5" style="background: rgba(26, 30, 58, 0.8);">
    <div class="container">
        <h2 class="section-title text-white mb-5">平台特色</h2>
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="glass-card p-4 text-center text-white h-100">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h5 class="mb-3">极速发货</h5>
                    <p class="mb-0">自动化系统，订单支付后2分钟内自动发货，全年无休</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="glass-card p-4 text-center text-white h-100">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h5 class="mb-3">安全保障</h5>
                    <p class="mb-0">银行级SSL加密，多重安全验证，保障每一笔交易安全</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="glass-card p-4 text-center text-white h-100">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h5 class="mb-3">专业客服</h5>
                    <p class="mb-0">7x24小时在线客服，专业技术团队，快速解决您的问题</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="glass-card p-4 text-center text-white h-100">
                    <div class="feature-icon mb-3">
                        <i class="fas fa-medal"></i>
                    </div>
                    <h5 class="mb-3">品质保证</h5>
                    <p class="mb-0">严格质量把控，所有商品经过多重验证，确保100%可用</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 实时购买动态 -->
<section class="py-5" style="background: rgba(10, 14, 39, 0.9);">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <h3 class="text-white mb-4">
                    <i class="fas fa-chart-line me-2 text-primary"></i>实时购买动态
                </h3>
                <div id="purchase-feed" class="purchase-feed" style="max-height: 400px; overflow-y: auto;">
                    <?php if(isset($purchaseInfos) && count($purchaseInfos) > 0): ?>
                        <?php $__currentLoopData = $purchaseInfos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $info): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="purchase-info">
                            <div class="d-flex align-items-center">
                                <div class="status-indicator status-online pulse"></div>
                                <div class="flex-grow-1">
                                    <span class="text-white"><?php echo e($info['email'], false); ?></span>
                                    <span class="text-light">购买了</span>
                                    <span class="text-primary fw-bold"><?php echo e($info['product'], false); ?></span>
                                    <span class="text-light">x<?php echo e($info['quantity'], false); ?></span>
                                </div>
                                <small class="text-muted"><?php echo e($info['time'], false); ?></small>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="glass-card p-4">
                    <h5 class="text-white mb-4">
                        <i class="fas fa-trophy me-2 text-warning"></i>今日销售统计
                    </h5>
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary mb-1" id="todaySales">1,234</h4>
                                <small class="text-light">今日订单</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-success mb-1" id="todayRevenue">¥89,456</h4>
                                <small class="text-light">今日营收</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-warning mb-1" id="onlineUsers">567</h4>
                                <small class="text-light">在线用户</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-info mb-1">99.8%</h4>
                                <small class="text-light">成功率</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 用户评价 -->
<section class="py-5" style="background: rgba(26, 30, 58, 0.8);">
    <div class="container">
        <h2 class="section-title text-white mb-5">用户评价</h2>
        <div class="row g-4">
            <div class="col-lg-4">
                <div class="testimonial-card">
                    <div class="mb-3">
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                    </div>
                    <p class="text-light mb-4">"发货速度超快，商品质量很好，客服态度也很棒，强烈推荐！"</p>
                    <div class="d-flex align-items-center">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <h6 class="text-white mb-0">张先生</h6>
                            <small class="text-muted">VIP用户</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="testimonial-card">
                    <div class="mb-3">
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                    </div>
                    <p class="text-light mb-4">"用了很多平台，这家是最靠谱的，价格合理，服务到位。"</p>
                    <div class="d-flex align-items-center">
                        <div class="bg-success rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <h6 class="text-white mb-0">李女士</h6>
                            <small class="text-muted">企业用户</small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="testimonial-card">
                    <div class="mb-3">
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                        <i class="fas fa-star text-warning"></i>
                    </div>
                    <p class="text-light mb-4">"自动发货系统很棒，半夜下单也能立即收到商品，太方便了！"</p>
                    <div class="d-flex align-items-center">
                        <div class="bg-info rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <h6 class="text-white mb-0">王先生</h6>
                            <small class="text-muted">普通用户</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 文章资讯 -->
<?php if(isset($articles) && count($articles) > 0): ?>
<section class="py-5" style="background: rgba(10, 14, 39, 0.8);">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-5">
            <h2 class="section-title text-white mb-0">最新资讯</h2>
            <a href="<?php echo e(url('/article'), false); ?>" class="btn btn-outline-light">
                查看更多 <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
        <div class="row g-4">
            <?php $__currentLoopData = $articles->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $article): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-4">
                <div class="glass-card p-0 overflow-hidden h-100">
                    <?php if($article->picture): ?>
                    <img src="<?php echo e(picture_url($article->picture), false); ?>"
                         alt="<?php echo e($article->title, false); ?>"
                         class="w-100"
                         style="height: 200px; object-fit: cover;">
                    <?php endif; ?>
                    <div class="p-4">
                        <h5 class="text-white mb-3"><?php echo e($article->title, false); ?></h5>
                        <p class="text-light mb-3"><?php echo e(Str::limit(strip_tags($article->content), 100), false); ?></p>
                        <div class="d-flex justify-content-between align-items-center">
                            <a href="<?php echo e(url("/article/{$article->id}"), false); ?>" class="btn btn-gradient btn-sm">
                                阅读更多
                            </a>
                            <small class="text-muted"><?php echo e($article->updated_at->format('m-d'), false); ?></small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('footer'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 数字动画效果
    function animateNumber(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current).toLocaleString() + (target >= 1000 ? '+' : '');
        }, 16);
    }

    // 启动数字动画
    const userCountEl = document.getElementById('userCount');
    const orderCountEl = document.getElementById('orderCount');
    const todaySalesEl = document.getElementById('todaySales');
    const todayRevenueEl = document.getElementById('todayRevenue');
    const onlineUsersEl = document.getElementById('onlineUsers');

    if (userCountEl) animateNumber(userCountEl, 10000);
    if (orderCountEl) animateNumber(orderCountEl, 50000);
    if (todaySalesEl) animateNumber(todaySalesEl, 1234);
    if (onlineUsersEl) animateNumber(onlineUsersEl, 567);

    // 实时购买动态模拟
    function addPurchaseInfo() {
        const feed = document.getElementById('purchase-feed');
        if (!feed) return;

        const emails = ['user***@gmail.com', 'buyer***@qq.com', 'customer***@163.com', 'vip***@outlook.com'];
        const products = ['Steam礼品卡', 'iTunes充值卡', 'Google Play卡', 'PayPal余额', 'Amazon礼品卡'];

        const email = emails[Math.floor(Math.random() * emails.length)];
        const product = products[Math.floor(Math.random() * products.length)];
        const quantity = Math.floor(Math.random() * 5) + 1;
        const time = new Date().toLocaleTimeString();

        const purchaseInfo = document.createElement('div');
        purchaseInfo.className = 'purchase-info animate__animated animate__fadeInRight';
        purchaseInfo.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="status-indicator status-online pulse"></div>
                <div class="flex-grow-1">
                    <span class="text-white">${email}</span>
                    <span class="text-light">购买了</span>
                    <span class="text-primary fw-bold">${product}</span>
                    <span class="text-light">x${quantity}</span>
                </div>
                <small class="text-muted">${time}</small>
            </div>
        `;

        feed.insertBefore(purchaseInfo, feed.firstChild);

        // 保持最多显示10条记录
        const items = feed.children;
        if (items.length > 10) {
            feed.removeChild(items[items.length - 1]);
        }
    }

    // 每10-30秒添加一条购买记录
    setInterval(addPurchaseInfo, Math.random() * 20000 + 10000);

    // 平滑滚动到锚点
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // 产品卡片悬停效果增强
    document.querySelectorAll('.product-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // 统计卡片计数动画
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                if (target.id === 'userCount' && !target.dataset.animated) {
                    animateNumber(target, 10000);
                    target.dataset.animated = 'true';
                }
                if (target.id === 'orderCount' && !target.dataset.animated) {
                    animateNumber(target, 50000);
                    target.dataset.animated = 'true';
                }
            }
        });
    }, observerOptions);

    if (userCountEl) observer.observe(userCountEl);
    if (orderCountEl) observer.observe(orderCountEl);

    console.log('Enterprise Neon Home Page initialized successfully');
});
</script>


<?php echo $__env->make('neon.layouts.default', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /www/wwwroot/www.bgoom.top/resources/views/neon/static_pages/home.blade.php ENDPATH**/ ?>