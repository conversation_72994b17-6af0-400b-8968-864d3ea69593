<!-- 核心JavaScript库 -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="/assets/neon/js/jquery-3.6.0.min.js"></script>

<!-- Neon主题JavaScript -->
<script src="/assets/neon/js/neon-config.js"></script>
<script src="/assets/neon/js/theme-switcher.js"></script>
<script src="/assets/neon/js/theme-colors.js"></script>
<script src="/assets/neon/js/scripts.js"></script>
<script src="/assets/neon/js/neon-components.js"></script>

<!-- 企业级增强JavaScript -->
<script src="/assets/neon/js/enterprise.js"></script>

<!-- 第三方插件 -->
<script src="/assets/neon/js/swiper-bundle.min.js"></script>
<script src="/assets/neon/js/simplebar.min.js"></script>
<script src="/assets/neon/js/choices.min.js"></script>
<script src="/assets/neon/js/glightbox.min.js"></script>
<script src="/assets/neon/js/fak.js"></script>
<script src="/assets/neon/js/paysvg.js"></script>
<script src="/assets/neon/js/isotope.pkgd.min.js"></script>

<!-- 初始化脚本 -->
<script>
// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 隐藏加载屏幕
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
        setTimeout(() => {
            loadingScreen.classList.add('fade-out');
            setTimeout(() => {
                loadingScreen.style.display = 'none';
                document.body.setAttribute('data-loading', 'false');
            }, 500);
        }, 1000);
    }

    // 初始化主题
    if (typeof window.Enterprise !== 'undefined') {
        console.log('Enterprise Framework loaded successfully');
    }

    // 初始化返回顶部按钮
    const backToTopBtn = document.getElementById('backToTop');
    if (backToTopBtn) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.style.display = 'flex';
            } else {
                backToTopBtn.style.display = 'none';
            }
        });

        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // 初始化客服按钮
    const customerServiceBtn = document.getElementById('customerService');
    if (customerServiceBtn) {
        customerServiceBtn.addEventListener('click', function() {
            // 这里可以添加客服功能
            if (typeof window.notify !== 'undefined') {
                window.notify('客服功能即将上线', 'info');
            } else {
                alert('客服功能即将上线');
            }
        });
    }

    // 初始化搜索按钮
    const searchBtn = document.getElementById('globalSearch');
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            // 这里可以添加搜索功能
            if (typeof window.notify !== 'undefined') {
                window.notify('搜索功能即将上线', 'info');
            } else {
                alert('搜索功能即将上线');
            }
        });
    }

    // Cookie同意处理
    const cookieConsent = document.getElementById('cookieConsent');
    const cookieAccept = document.getElementById('cookieAccept');
    const cookieDecline = document.getElementById('cookieDecline');

    if (cookieConsent && !localStorage.getItem('cookieConsent')) {
        setTimeout(() => {
            cookieConsent.style.display = 'block';
        }, 2000);
    }

    if (cookieAccept) {
        cookieAccept.addEventListener('click', function() {
            localStorage.setItem('cookieConsent', 'accepted');
            cookieConsent.style.display = 'none';
        });
    }

    if (cookieDecline) {
        cookieDecline.addEventListener('click', function() {
            localStorage.setItem('cookieConsent', 'declined');
            cookieConsent.style.display = 'none';
        });
    }

    // PWA安装提示
    let deferredPrompt;
    const pwaInstallPrompt = document.getElementById('pwaInstallPrompt');
    const pwaInstall = document.getElementById('pwaInstall');
    const pwaDecline = document.getElementById('pwaDecline');

    window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        if (pwaInstallPrompt && !localStorage.getItem('pwaDeclined')) {
            setTimeout(() => {
                pwaInstallPrompt.style.display = 'block';
            }, 5000);
        }
    });

    if (pwaInstall) {
        pwaInstall.addEventListener('click', async () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                if (outcome === 'accepted') {
                    console.log('PWA安装成功');
                }
                deferredPrompt = null;
                pwaInstallPrompt.style.display = 'none';
            }
        });
    }

    if (pwaDecline) {
        pwaDecline.addEventListener('click', () => {
            localStorage.setItem('pwaDeclined', 'true');
            pwaInstallPrompt.style.display = 'none';
        });
    }
});

// 性能监控（仅开发环境）
<?php if(config('app.debug')): ?>
window.addEventListener('load', function() {
    const loadTime = performance.now();
    const domNodes = document.querySelectorAll('*').length;
    const memoryUsage = performance.memory ? Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) : 'N/A';

    const loadTimeEl = document.getElementById('load-time');
    const domNodesEl = document.getElementById('dom-nodes');
    const memoryUsageEl = document.getElementById('memory-usage');

    if (loadTimeEl) loadTimeEl.textContent = Math.round(loadTime);
    if (domNodesEl) domNodesEl.textContent = domNodes;
    if (memoryUsageEl) memoryUsageEl.textContent = memoryUsage;
});
<?php endif; ?>
</script>
<?php /**PATH /www/wwwroot/www.bgoom.top/resources/views/neon/layouts/_script.blade.php ENDPATH**/ ?>