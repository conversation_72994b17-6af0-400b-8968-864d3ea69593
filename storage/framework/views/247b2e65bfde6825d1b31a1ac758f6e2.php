<?php $__env->startSection('header'); ?>
<style>
/* 企业级注册页面样式 */
.register-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.register-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.register-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

.form-control-glass {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    padding: 1rem 1.5rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.form-control-glass:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    color: white;
}

.form-control-glass::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.register-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: 2rem;
}

.password-strength {
    height: 4px;
    border-radius: 2px;
    margin-top: 0.5rem;
    transition: all 0.3s ease;
}

.strength-weak { background: #dc3545; }
.strength-medium { background: #ffc107; }
.strength-strong { background: #28a745; }

.bonus-alert {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border: none;
    border-radius: 15px;
    color: white;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.feature-list li i {
    color: #4facfe;
    margin-right: 0.5rem;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: floatUpDown 8s ease-in-out infinite;
}

@keyframes floatUpDown {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    z-index: 10;
}

.password-toggle:hover {
    color: white;
}

.math-question-card {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    margin-bottom: 1rem;
}

.terms-checkbox {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

.terms-checkbox:checked {
    background: var(--primary-gradient);
    border-color: rgba(102, 126, 234, 0.5);
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- 企业级注册页面 -->
<section class="register-hero d-flex align-items-center">
    <!-- 浮动装饰元素 -->
    <div class="floating-elements">
        <div class="floating-element" style="left: 10%; top: 20%; font-size: 2rem;">
            <i class="fas fa-user-plus"></i>
        </div>
        <div class="floating-element" style="left: 80%; top: 30%; font-size: 1.5rem;">
            <i class="fas fa-gift"></i>
        </div>
        <div class="floating-element" style="left: 15%; top: 70%; font-size: 1.8rem;">
            <i class="fas fa-star"></i>
        </div>
        <div class="floating-element" style="left: 75%; top: 80%; font-size: 1.3rem;">
            <i class="fas fa-crown"></i>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-lg-10">
                <div class="row g-0">
                    <!-- 左侧信息 -->
                    <div class="col-lg-6 d-flex align-items-center">
                        <div class="text-white p-5">
                            <h2 class="display-5 fw-bold mb-4">
                                加入我们！
                            </h2>
                            <p class="lead mb-4">
                                创建您的账户，开启专业的数字商品购买之旅
                            </p>

                            <?php if(dujiaoka_config_get('regmoney') != 0): ?>
                            <div class="bonus-alert">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-gift fa-2x me-3"></i>
                                    <div>
                                        <h6 class="mb-1">新用户专享</h6>
                                        <p class="mb-0">注册即送 <?php echo e(dujiaoka_config_get('regmoney'), false); ?> <?php echo e(dujiaoka_config_get('global_currency'), false); ?></p>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <ul class="feature-list">
                                <li>
                                    <i class="fas fa-check-circle"></i>
                                    免费注册，无隐藏费用
                                </li>
                                <li>
                                    <i class="fas fa-check-circle"></i>
                                    安全的账户保护机制
                                </li>
                                <li>
                                    <i class="fas fa-check-circle"></i>
                                    个性化的购买推荐
                                </li>
                                <li>
                                    <i class="fas fa-check-circle"></i>
                                    专属会员优惠权益
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- 右侧注册表单 -->
                    <div class="col-lg-6">
                        <?php if(dujiaoka_config_get('is_open_reg') == \App\Models\BaseModel::STATUS_OPEN): ?>
                        <div class="register-card">
                            <h3 class="register-title fw-bold">用户注册</h3>

                            <form id="register-form" action="<?php echo e(url('register'), false); ?>" method="post" novalidate>
                                <?php echo csrf_field(); ?>
                                <!-- 邮箱输入 -->
                                <div class="mb-4">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-envelope me-2"></i>邮箱地址
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" name="email" class="form-control-glass"
                                           placeholder="请输入您的邮箱地址" required>
                                    <div class="invalid-feedback">请输入有效的邮箱地址!</div>
                                </div>

                                <!-- 密码输入 -->
                                <div class="mb-4">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-lock me-2"></i>登录密码
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="position-relative">
                                        <input type="password" name="password" id="password" class="form-control-glass"
                                               placeholder="请输入密码（至少6位）" required minlength="6">
                                        <button type="button" class="password-toggle" onclick="togglePassword('password', 'passwordToggleIcon')">
                                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                        </button>
                                    </div>
                                    <div class="password-strength" id="passwordStrength"></div>
                                    <small class="text-light mt-1 d-block">密码强度：<span id="strengthText">请输入密码</span></small>
                                    <div class="invalid-feedback">密码至少需要6位字符!</div>
                                </div>

                                <!-- 确认密码 -->
                                <div class="mb-4">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-lock me-2"></i>确认密码
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="position-relative">
                                        <input type="password" name="confirm_password" id="confirmPassword" class="form-control-glass"
                                               placeholder="请再次输入密码" required>
                                        <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword', 'confirmPasswordToggleIcon')">
                                            <i class="fas fa-eye" id="confirmPasswordToggleIcon"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">两次输入的密码不一致!</div>
                                </div>

                                <!-- 邀请码 -->
                                <div class="mb-4">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-gift me-2"></i>邀请码
                                        <small class="text-muted">（可选）</small>
                                    </label>
                                    <input type="text" name="invite_code" class="form-control-glass"
                                           placeholder="如有邀请码请输入">
                                    <small class="text-light mt-1 d-block">
                                        <i class="fas fa-info-circle me-1"></i>
                                        输入邀请码可获得额外奖励
                                    </small>
                                </div>

                                <!-- 数学验证题 -->
                                <?php if(dujiaoka_config_get('is_openreg_img_code') == \App\Models\Goods::STATUS_OPEN): ?>
                                <div class="mb-4">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-calculator me-2"></i>安全验证
                                    </label>
                                    <div class="math-question-card">
                                        <h5 class="text-white mb-2" id="math-question">请计算：</h5>
                                        <button type="button" class="btn btn-outline-light btn-sm" id="refresh">
                                            <i class="fas fa-refresh me-1"></i>换一题
                                        </button>
                                    </div>
                                    <input type="text" name="math_answer" class="form-control-glass"
                                           placeholder="请输入计算结果" required>
                                </div>
                                <?php endif; ?>

                                <!-- 服务条款 -->
                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input terms-checkbox" type="checkbox" id="agreeTerms" required>
                                        <label class="form-check-label text-white" for="agreeTerms">
                                            我已阅读并同意
                                            <a href="#" class="text-primary text-decoration-none">《用户服务协议》</a>
                                            和
                                            <a href="#" class="text-primary text-decoration-none">《隐私政策》</a>
                                        </label>
                                    </div>
                                </div>

                                <!-- 注册按钮 -->
                                <button type="submit" class="btn btn-gradient w-100 py-3 fw-bold mb-4" id="submitBtn">
                                    <i class="fas fa-user-plus me-2"></i>立即注册
                                </button>

                                <!-- 登录链接 -->
                                <div class="text-center">
                                    <p class="text-light mb-0">
                                        已有账户？
                                        <a href="<?php echo e(url('login'), false); ?>" class="text-primary text-decoration-none fw-bold">
                                            立即登录
                                        </a>
                                    </p>
                                </div>
                            </form>
                        </div>
                        <?php else: ?>
                        <!-- 注册功能关闭提示 -->
                        <div class="register-card text-center">
                            <i class="fas fa-user-times fa-4x text-warning mb-4"></i>
                            <h4 class="text-white mb-3">注册暂时关闭</h4>
                            <p class="text-light mb-4">用户注册功能暂时关闭，请稍后再试</p>
                            <a href="/" class="btn btn-gradient">
                                <i class="fas fa-home me-2"></i>返回首页
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('footer'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 密码显示/隐藏切换
    window.togglePassword = function(inputId, iconId) {
        const passwordInput = document.getElementById(inputId);
        const toggleIcon = document.getElementById(iconId);

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    };

    // 密码强度检测
    const passwordInput = document.getElementById('password');
    const strengthBar = document.getElementById('passwordStrength');
    const strengthText = document.getElementById('strengthText');

    if (passwordInput && strengthBar && strengthText) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);

            strengthBar.className = 'password-strength';

            if (password.length === 0) {
                strengthText.textContent = '请输入密码';
                strengthBar.style.width = '0%';
            } else if (strength < 3) {
                strengthBar.classList.add('strength-weak');
                strengthBar.style.width = '33%';
                strengthText.textContent = '弱';
            } else if (strength < 5) {
                strengthBar.classList.add('strength-medium');
                strengthBar.style.width = '66%';
                strengthText.textContent = '中等';
            } else {
                strengthBar.classList.add('strength-strong');
                strengthBar.style.width = '100%';
                strengthText.textContent = '强';
            }
        });
    }

    function calculatePasswordStrength(password) {
        let strength = 0;

        // 长度检查
        if (password.length >= 6) strength++;
        if (password.length >= 8) strength++;

        // 字符类型检查
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;

        return strength;
    }

    // 确认密码验证
    const confirmPasswordInput = document.getElementById('confirmPassword');
    if (confirmPasswordInput && passwordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            if (this.value !== passwordInput.value) {
                this.setCustomValidity('两次输入的密码不一致');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    // 数学验证题生成
    <?php if(dujiaoka_config_get('is_openreg_img_code') == \App\Models\Goods::STATUS_OPEN): ?>
    function generateMathQuestion() {
        const num1 = Math.floor(Math.random() * 50) + 1;
        const num2 = Math.floor(Math.random() * 50) + 1;
        const operators = ['+', '-', '*'];
        const operator = operators[Math.floor(Math.random() * operators.length)];

        let question = `${num1} ${operator} ${num2} = ?`;
        let answer;

        switch(operator) {
            case '+':
                answer = num1 + num2;
                break;
            case '-':
                answer = num1 - num2;
                break;
            case '*':
                answer = num1 * num2;
                break;
        }

        document.getElementById('math-question').textContent = `请计算：${question}`;
        document.getElementById('math-question').dataset.answer = answer;
    }

    // 初始化数学题
    generateMathQuestion();

    // 刷新数学题
    document.getElementById('refresh').addEventListener('click', generateMathQuestion);
    <?php endif; ?>

    // 表单提交处理
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');

            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>注册中...';

            // 验证表单
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();

                // 重置按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-user-plus me-2"></i>立即注册';
            }

            this.classList.add('was-validated');
        });
    }

    // 输入框焦点效果
    document.querySelectorAll('.form-control-glass').forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });

    console.log('Enterprise Register Page initialized successfully');
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('neon.layouts.default', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /www/wwwroot/www.bgoom.top/resources/views/neon/static_pages/register.blade.php ENDPATH**/ ?>