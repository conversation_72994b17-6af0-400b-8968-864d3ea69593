<?php $__env->startSection('header'); ?>
<style>
/* 企业级登录页面样式 */
.login-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.login-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

.form-control-glass {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    padding: 1rem 1.5rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.form-control-glass:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    color: white;
}

.form-control-glass::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.login-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: center;
    margin-bottom: 2rem;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: floatUpDown 8s ease-in-out infinite;
}

@keyframes floatUpDown {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    z-index: 10;
}

.password-toggle:hover {
    color: white;
}

.math-question-card {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    margin-bottom: 1rem;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.feature-list li i {
    color: #4facfe;
    margin-right: 0.5rem;
}
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- 企业级登录页面 -->
<section class="login-hero d-flex align-items-center">
    <!-- 浮动装饰元素 -->
    <div class="floating-elements">
        <div class="floating-element" style="left: 10%; top: 20%; font-size: 2rem;">
            <i class="fas fa-shield-alt"></i>
        </div>
        <div class="floating-element" style="left: 80%; top: 30%; font-size: 1.5rem;">
            <i class="fas fa-lock"></i>
        </div>
        <div class="floating-element" style="left: 15%; top: 70%; font-size: 1.8rem;">
            <i class="fas fa-user-shield"></i>
        </div>
        <div class="floating-element" style="left: 75%; top: 80%; font-size: 1.3rem;">
            <i class="fas fa-key"></i>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-lg-10">
                <div class="row g-0">
                    <!-- 左侧信息 -->
                    <div class="col-lg-6 d-flex align-items-center">
                        <div class="text-white p-5">
                            <h2 class="display-5 fw-bold mb-4">
                                欢迎回来！
                            </h2>
                            <p class="lead mb-4">
                                登录您的账户，享受专业的数字商品服务
                            </p>
                            <ul class="feature-list">
                                <li>
                                    <i class="fas fa-check-circle"></i>
                                    安全可靠的账户保护
                                </li>
                                <li>
                                    <i class="fas fa-check-circle"></i>
                                    个性化的购买体验
                                </li>
                                <li>
                                    <i class="fas fa-check-circle"></i>
                                    订单历史记录查看
                                </li>
                                <li>
                                    <i class="fas fa-check-circle"></i>
                                    专属客服支持
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- 右侧登录表单 -->
                    <div class="col-lg-6">
                        <?php if(dujiaoka_config_get('is_open_login') == \App\Models\BaseModel::STATUS_OPEN): ?>
                        <div class="login-card">
                            <h3 class="login-title fw-bold">用户登录</h3>

                            <form id="login-form" action="<?php echo e(url('login'), false); ?>" method="post" novalidate>
                                <?php echo csrf_field(); ?>

                                <!-- 邮箱输入 -->
                                <div class="mb-4">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-envelope me-2"></i>邮箱地址
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" name="email" class="form-control-glass"
                                           placeholder="请输入您的邮箱地址" required>
                                    <div class="invalid-feedback">请输入有效的邮箱地址!</div>
                                </div>

                                <!-- 密码输入 -->
                                <div class="mb-4">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-lock me-2"></i>登录密码
                                        <span class="text-danger">*</span>
                                    </label>
                                    <div class="position-relative">
                                        <input type="password" name="password" id="password" class="form-control-glass"
                                               placeholder="请输入您的密码" required>
                                        <button type="button" class="password-toggle" onclick="togglePassword()">
                                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback">请输入您的密码!</div>
                                </div>

                                <!-- 数学验证题 -->
                                <?php if(dujiaoka_config_get('is_openlogin_img_code') == \App\Models\Goods::STATUS_OPEN): ?>
                                <div class="mb-4">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-calculator me-2"></i>安全验证
                                    </label>
                                    <div class="math-question-card">
                                        <h5 class="text-white mb-2" id="math-question">请计算：</h5>
                                        <button type="button" class="btn btn-outline-light btn-sm" id="refresh">
                                            <i class="fas fa-refresh me-1"></i>换一题
                                        </button>
                                    </div>
                                    <input type="text" name="math_answer" class="form-control-glass"
                                           placeholder="请输入计算结果" required>
                                </div>
                                <?php endif; ?>

                                <!-- 记住我 -->
                                <div class="mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                        <label class="form-check-label text-white" for="remember">
                                            记住我的登录状态
                                        </label>
                                    </div>
                                </div>

                                <!-- 登录按钮 -->
                                <button type="submit" class="btn btn-gradient w-100 py-3 fw-bold mb-4">
                                    <i class="fas fa-sign-in-alt me-2"></i>立即登录
                                </button>

                                <!-- 注册链接 -->
                                <div class="text-center">
                                    <p class="text-light mb-0">
                                        还没有账户？
                                        <a href="<?php echo e(url('register'), false); ?>" class="text-primary text-decoration-none fw-bold">
                                            立即注册
                                        </a>
                                    </p>
                                </div>
                            </form>
                        </div>
                        <?php else: ?>
                        <!-- 登录功能关闭提示 -->
                        <div class="login-card text-center">
                            <i class="fas fa-tools fa-4x text-warning mb-4"></i>
                            <h4 class="text-white mb-3">系统维护中</h4>
                            <p class="text-light mb-4">登录功能暂时关闭，我们正在进行系统升级</p>
                            <a href="/" class="btn btn-gradient">
                                <i class="fas fa-home me-2"></i>返回首页
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('footer'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 密码显示/隐藏切换
    window.togglePassword = function() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('passwordToggleIcon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    };

    // 数学验证题生成
    <?php if(dujiaoka_config_get('is_openlogin_img_code') == \App\Models\Goods::STATUS_OPEN): ?>
    function generateMathQuestion() {
        const num1 = Math.floor(Math.random() * 10) + 1;
        const num2 = Math.floor(Math.random() * 10) + 1;
        const operators = ['+', '-', '*'];
        const operator = operators[Math.floor(Math.random() * operators.length)];

        let question = `${num1} ${operator} ${num2} = ?`;
        let answer;

        switch(operator) {
            case '+':
                answer = num1 + num2;
                break;
            case '-':
                answer = num1 - num2;
                break;
            case '*':
                answer = num1 * num2;
                break;
        }

        document.getElementById('math-question').textContent = `请计算：${question}`;
        document.getElementById('math-question').dataset.answer = answer;
    }

    // 初始化数学题
    generateMathQuestion();

    // 刷新数学题
    document.getElementById('refresh').addEventListener('click', generateMathQuestion);
    <?php endif; ?>

    // 表单提交处理
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');

            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>登录中...';

            // 验证表单
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();

                // 重置按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>立即登录';
            }

            this.classList.add('was-validated');
        });
    }

    // 输入框焦点效果
    document.querySelectorAll('.form-control-glass').forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });

    console.log('Enterprise Login Page initialized successfully');
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('neon.layouts.default', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH /www/wwwroot/www.bgoom.top/resources/views/neon/static_pages/login.blade.php ENDPATH**/ ?>