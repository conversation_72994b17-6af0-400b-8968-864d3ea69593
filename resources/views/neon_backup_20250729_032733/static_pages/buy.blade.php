@extends('neon.layouts.seo')
@section('content')
<div class="content">
    <div class="container">
      <main class="content-wrapper">

        <nav class="container pt-2 pt-xxl-3 my-3 my-md-4" aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url('/') }}">主页</a></li>
            <li class="breadcrumb-item"><a href="{{ url('/') }}">购物</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $gd_name }}</li>
          </ol>
        </nav>

        <!-- 商品主内容区域 -->
        <section class="container">
          <div class="row">
            <!-- 左侧：商品图片 / Gallery -->
            <div class="col-md-6 pb-4 pb-md-0 mb-2 mb-sm-3 mb-md-0">
              <div class="position-relative">
                <a class="hover-effect-scale hover-effect-opacity position-relative d-flex rounded overflow-hidden mb-3 mb-sm-4 mb-md-3 mb-lg-4"
                   href="{{ picture_url($picture) }}" data-glightbox="" data-gallery="product-gallery">
                  <i class="ci-zoom-in hover-effect-target fs-3 text-white position-absolute top-50 start-50 translate-middle opacity-0 z-2"></i>
                  <div class="ratio hover-effect-target bg-body-tertiary rounded"
                       style="--cz-aspect-ratio: calc(706 / 636 * 100%)">
                    <img src="{{ picture_url($picture) }}" alt="{{ $gd_name }}">
                  </div>
                </a>
              </div>
            </div>

            <!-- 右侧：商品详情 & 购买表单 -->
            <div class="col-md-6">
              <div class="ps-md-4 ps-xl-5">

                <!-- 商品名和切换按钮并排 -->
                <div class="d-flex align-items-center mb-3">
                  <h1 class="h3 mb-0">{{ $gd_name }}</h1>
                  @if (!empty($goods_sub))
                  <div class="ms-4 d-flex align-items-center">
                    <button type="button" class="btn btn-outline-primary btn-lg d-flex align-items-center me-2 active" id="specBtnMode">
                      <svg xmlns="http://www.w3.org/2000/svg" class="me-1" width="22" height="22" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M2 2h12v2H2V2zm0 4h12v2H2V6zm0 4h12v2H2v-2zm0 4h12v2H2v-2z"/>
                      </svg>
                      按钮模式
                    </button>
                    <button type="button" class="btn btn-outline-success btn-lg d-flex align-items-center" id="specSelectMode">
                      <svg xmlns="http://www.w3.org/2000/svg" class="me-1" width="22" height="22" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M3 5a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v6a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V5zm1 0v6h8V5H4zm2 2.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5z"/>
                      </svg>
                      下拉模式
                    </button>
                  </div>
                  @endif
                </div>

                <!-- 购买表单 -->
                <form class="needs-validation" novalidate id="buy-form" action="{{ url('create-order') }}" method="post">
                  @csrf
                  <!-- 重要：始终包含商品ID -->
                  <input type="hidden" name="gid" value="{{ $id }}">

                  <!-- 多规格选择区 -->
                  @if (!empty($goods_sub))
                  <div class="mb-3">
                    <label class="form-label"><b>选择规格</b></label>
                    
                    <!-- 按钮模式 (默认显示) -->
                    <div class="btn-group d-block" id="btnModeContainer">
                      @foreach ($goods_sub as $ki=>$sub)
                      <input type="radio" class="btn-check spec-radio" name="sub_id" id="subid-{{ $sub['id'] }}"
                            value="{{ $sub['id'] }}" data-price="{{ $sub['price'] }}" data-stock="{{ $sub['stock'] }}" @if($ki==0) checked @endif>
                      <label class="btn btn-outline-primary mb-1 spec-btn" for="subid-{{ $sub['id'] }}">
                        {{ $sub['name'] }} (￥{{ number_format($sub['price'],2) }})
                      </label>
                      @endforeach
                    </div>
                    
                    <!-- 下拉模式 (默认隐藏) -->
                    <select class="form-select d-none" id="selectModeContainer" name="sub_id_select">
                      @foreach ($goods_sub as $ki=>$sub)
                      <option value="{{ $sub['id'] }}" data-price="{{ $sub['price'] }}" data-stock="{{ $sub['stock'] }}" @if($ki==0) selected @endif>
                        {{ $sub['name'] }} (￥{{ number_format($sub['price'],2) }})
                      </option>
                      @endforeach
                    </select>
                  </div>
                  @endif

                  <!-- 关键信息 (如：自动/人工、库存、批发价提示等) -->
                  <div class="d-flex flex-wrap gap-3 gap-xxl-4 fs-sm text-dark-emphasis mb-2">
                    <div class="d-flex align-items-center me-3">
                      <i class="ci-delivery fs-xl text-body-emphasis me-2"></i>
                      @if($type == \App\Models\Goods::AUTOMATIC_DELIVERY)
                        自动发货
                      @else
                        人工发货
                      @endif
                    </div>
                    <div class="d-flex align-items-center me-3">
                      <i class="ci-broccoli fs-xl text-body-emphasis me-2"></i>
                      库存：<span id="stockText">{{ $stock }}</span>
                    </div>
                    @if(!empty($wholesale_price_cnf) && is_array($wholesale_price_cnf))
                      <div class="d-flex align-items-center me-3">
                        <i class="ci-leaf fs-xl text-body-emphasis me-2"></i>
                        有批发价
                      </div>
                    @endif
                  </div>

                  <!-- 价格显示 -->
                  <div class="h4 d-flex align-items-center my-3">
                    单价：<span id="actualPrice">{{ $price }}</span> {{ __('dujiaoka.money_symbol') }}
                  </div>

                  <!-- 邮箱 -->
                  <div class="mb-3">
                    <label for="email" class="form-label">
                      {{ __('dujiaoka.email') }} <span class="text-danger">*</span>
                    </label>
                    <input type="email" class="form-control" name="email" id="email" required placeholder="查询订单或发送卡密会用到">
                    <div class="invalid-feedback">请输入您的电子邮箱!</div>
                  </div>

                  <!-- 查询密码 -->
                  @if(dujiaoka_config_get('is_open_search_pwd') == \App\Models\Goods::STATUS_OPEN)
                  <div class="mb-3">
                    <label for="search_pwd" class="form-label">
                      {{ __('dujiaoka.search_password') }} <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" id="search_pwd" name="search_pwd" required placeholder="查询订单时会用到">
                    <div class="invalid-feedback">请输入您的查询密码!</div>
                  </div>
                  @endif

                  <!-- 其它表单字段 -->
                  @if(($type ?? ($goods['type'] ?? null)) == \App\Models\Goods::MANUAL_PROCESSING && !empty($other_ipu) && is_array($other_ipu))
                  @foreach($other_ipu as $ipu)
                  <div class="mb-3">
                    <label for="{{ $ipu['field'] }}" class="form-label">
                      {{ $ipu['desc'] }}:
                      @if($ipu['rule'] !== false)
                        <span class="text-danger">*</span>
                      @endif
                    </label>
                    <input type="text"
                           class="form-control"
                           id="{{ $ipu['field'] }}"
                           name="{{ $ipu['field'] }}"
                           @if($ipu['rule'] !== false) required @endif
                           placeholder="{{ $ipu['desc'] }}">
                    @if($ipu['rule'] !== false)
                    <div class="invalid-feedback">请填写 {{ $ipu['desc'] }}！</div>
                    @endif
                  </div>
                  @endforeach
                  @endif

                  <!-- 优惠券 -->
                  @if(isset($open_coupon))
                  <div class="mb-3">
                    <label for="coupon" class="form-label">
                      {{ __('dujiaoka.coupon_code') }}:
                    </label>
                    <input type="text" class="form-control" id="coupon" name="coupon_code" placeholder="">
                  </div>
                  @endif

                  <!-- 图片验证码 -->
                  @if(dujiaoka_config_get('is_open_img_code') == \App\Models\Goods::STATUS_OPEN)
                  <div class="mb-3">
                    <label for="verifyCode" class="form-label">{{ __('dujiaoka.img_verify_code') }}</label>
                    <div class="input-group">
                      <input type="text" name="img_verify_code" class="form-control" id="verifyCode" required>
                      <img style="margin-left: 10px;"
                           src="{{ captcha_src('buy') . time() }}" height="33px"
                           alt="{{ __('dujiaoka.img_verify_code') }}"
                           onclick="refreshCaptcha()" id="imageCode">
                    </div>
                  </div>
                  <script>
                    function refreshCaptcha() {
                      var img = document.getElementById('imageCode');
                      img.src = '{{ captcha_src('buy') }}' + Math.random();
                    }
                  </script>
                  @endif

                  <!-- 支付方式 -->
                  <div class="mb-4">
                    <label class="form-label fw-semibold pb-1 mb-2">
                      {{ __('dujiaoka.payment_method') }} <span class="text-danger">*</span>:
                    </label>
                    <div class="d-flex flex-wrap gap-2" id="paymentGroup">
                      @foreach($payways as $index => $way)
                      <label class="payments" data-type="{{ $way['pay_check'] }}" data-id="{{ $way['id'] }}">
                        <input type="radio" class="btn-check"
                               name="payway" value="{{ $way['id'] }}"
                               id="payway-{{ $way['id'] }}"
                               @if($index == 0) checked @endif>
                        <span class="btn btn-image p-0 paymentsvg">{{ $way['pay_name'] }}</span>
                        <span>{{ $way['pay_name'] }}</span>
                      </label>
                      @endforeach
                    </div>
                  </div>

                  <!-- 购买数量 -->
                  <div class="d-flex gap-3 pb-3 pb-lg-4 mb-3">
                    <div class="count-input flex-shrink-0 w-50 d-flex justify-content-center align-items-center">
                      <button type="button" class="btn btn-icon btn-lg" id="decrementBtn">
                        <i class="ci-minus"></i>
                      </button>
                      <input type="number" class="form-control form-control-xl w-50" name="by_amount" min="1" value="1" id="byAmountBox">
                      <button type="button" class="btn btn-icon btn-lg" id="incrementBtn">
                        <i class="ci-plus"></i>
                      </button>
                    </div>
                    <input type="hidden" name="aff" value="">
                    <button type="submit" id="submitBtn" class="btn btn-lg btn-dark w-100">
                      {{ __('dujiaoka.order_now') }}
                    </button>
                  </div>
                  
                  <!-- 总价显示区域 - 更新为您要求的格式 -->
                  <div class="mb-3 d-flex align-items-center justify-content-between">
                    <span class="h4 mb-0">总计：</span>
                    <span class="h4 mb-0 text-primary">
                      <span id="totalPrice">{{ $price }}</span> {{ __('dujiaoka.money_symbol') }}
                    </span>
                  </div>

                  <!-- 额外提示 -->
                  <ul class="list-unstyled gap-3 pb-3 pb-lg-4 mb-3 fs-sm">
                    <li class="d-flex flex-wrap">
                      <span class="d-flex align-items-center fw-medium text-dark-emphasis me-2">
                        <i class="ci-clock fs-base me-2"></i>
                        推荐使用加密货币支付:
                      </span>
                      注意正确的付款金额
                    </li>
                    @if(($type ?? ($goods['type'] ?? null)) == \App\Models\Goods::MANUAL_PROCESSING)
                    <li class="d-flex flex-wrap">
                      <span class="d-flex align-items-center fw-medium text-dark-emphasis me-2">
                        <i class="ci-delivery fs-base me-2"></i>
                        手动发货商品:
                      </span>
                      请在规定的作息时间联系客服
                    </li>
                    @endif
                  </ul>
                </form>
              </div>
            </div>
          </div>
        </section>

        <!-- 商品Tab部分 -->
        <section class="container pt-5 mt-2 mt-sm-3 mt-lg-4 mt-xl-5" >
          <ul class="nav nav-underline flex-nowrap border-bottom" role="tablist">
            <li class="nav-item me-md-1" role="presentation">
              <button type="button" class="nav-link active" id="description-tab" data-bs-toggle="tab"
                      data-bs-target="#description-tab-pane" role="tab"
                      aria-controls="description-tab-pane" aria-selected="true">
                {{ __('goods.fields.description') }}
              </button>
            </li>
            <li class="nav-item me-md-1" role="presentation">
              <button type="button" class="nav-link" id="washing-tab" data-bs-toggle="tab"
                      data-bs-target="#washing-tab-pane" role="tab"
                      aria-controls="washing-tab-pane" aria-selected="false">
                商品提示
              </button>
            </li>
            <li class="nav-item me-md-1" role="presentation">
              <button type="button" class="nav-link" id="delivery-tab" data-bs-toggle="tab"
                      data-bs-target="#delivery-tab-pane" role="tab"
                      aria-controls="delivery-tab-pane" aria-selected="false">
                相关教程
              </button>
            </li>
          </ul>
          <div class="tab-content pt-4 mt-sm-1 mt-md-3">
            <div class="tab-pane fade active show" id="description-tab-pane" role="tabpanel" aria-labelledby="description-tab">
              <div class="row">
                {!! $description ?? ($goods['description'] ?? '') !!}
              </div>
            </div>
            <!-- ...其它tab... -->
          </div>
        </section>
      </main>
    </div>
</div>

<!-- Modal & 脚本区 -->
<div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="staticBackdropLabel">{{ __('goods.fields.buy_prompt') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {!! $buy_prompt !!}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('dujiaoka.close') }}</button>
            </div>
        </div>
    </div>
</div>
@stop

@section('js')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取Modal实例
    var myModal = new bootstrap.Modal(document.getElementById('staticBackdrop'));
    
    // 如果有购买提示，显示Modal
    @if(!empty($buy_prompt))
    myModal.show();
    @endif
    
    // 获取DOM元素
    const btnModeBtn = document.getElementById('specBtnMode');
    const selectModeBtn = document.getElementById('specSelectMode');
    const btnContainer = document.getElementById('btnModeContainer');
    const selectContainer = document.getElementById('selectModeContainer');
    const buyForm = document.getElementById('buy-form');
    const decrementBtn = document.getElementById('decrementBtn');
    const incrementBtn = document.getElementById('incrementBtn');
    const amountInput = document.getElementById('byAmountBox');
    const submitBtn = document.getElementById('submitBtn');
    const stockText = document.getElementById('stockText');
    const actualPrice = document.getElementById('actualPrice');
    const totalPrice = document.getElementById('totalPrice');
    
    // 计算并更新总价
    function updateTotalPrice() {
        const price = parseFloat(actualPrice.textContent);
        const amount = parseInt(amountInput.value) || 1; // 添加默认值1，防止NaN
        const total = (price * amount).toFixed(2);
        totalPrice.textContent = total;
    }
    
    // 规格模式切换功能
    if (btnModeBtn && selectModeBtn) {
        btnModeBtn.addEventListener('click', function() {
            btnModeBtn.classList.add('active');
            selectModeBtn.classList.remove('active');
            btnContainer.classList.remove('d-none');
            selectContainer.classList.add('d-none');
        });
        
        selectModeBtn.addEventListener('click', function() {
            btnModeBtn.classList.remove('active');
            selectModeBtn.classList.add('active');
            btnContainer.classList.add('d-none');
            selectContainer.classList.remove('d-none');
        });
    }
    
    // 按钮模式规格选择
    const specRadios = document.querySelectorAll('.spec-radio');
    specRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                // 更新价格和库存显示
                updatePriceAndStock(this.dataset.price, this.dataset.stock);
                
                // 同步下拉框选择
                if (selectContainer) {
                    selectContainer.value = this.value;
                }
                
                // 更新总价
                updateTotalPrice();
            }
        });
    });
    
    // 下拉模式规格选择
    if (selectContainer) {
        selectContainer.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const price = selectedOption.dataset.price;
            const stock = selectedOption.dataset.stock;
            const value = selectedOption.value;
            
            // 更新价格和库存显示
            updatePriceAndStock(price, stock);
            
            // 同步单选按钮选择
            const radio = document.getElementById('subid-' + value);
            if (radio) {
                radio.checked = true;
            }
            
            // 更新总价
            updateTotalPrice();
        });
    }
    
    // 更新价格和库存显示
    function updatePriceAndStock(price, stock) {
        if (actualPrice) actualPrice.textContent = price;
        if (stockText) stockText.textContent = stock;
        
        // 更新数量输入框的最大值
        if (amountInput) {
            amountInput.max = stock;
            
            // 如果当前数量超过库存，调整为库存数量
            if (parseInt(amountInput.value) > parseInt(stock)) {
                amountInput.value = stock;
            }
        }
    }
    
    // 数量加减按钮
    if (decrementBtn && incrementBtn && amountInput) {
        decrementBtn.addEventListener('click', function() {
            if (parseInt(amountInput.value) > 1) {
                amountInput.value = parseInt(amountInput.value) - 1;
                updateTotalPrice();
            }
        });
        
        incrementBtn.addEventListener('click', function() {
            const maxStock = parseInt(stockText.textContent);
            if (parseInt(amountInput.value) < maxStock) {
                amountInput.value = parseInt(amountInput.value) + 1;
                updateTotalPrice();
            }
        });
        
        // 修复手动输入问题 - 使用多种事件监听
        ['change', 'input', 'keyup', 'blur'].forEach(function(eventType) {
            amountInput.addEventListener(eventType, function() {
                // 验证输入并更新总价
                const maxStock = parseInt(stockText.textContent);
                const inputValue = parseInt(this.value) || 1;
                
                if (inputValue < 1) {
                    this.value = 1;
                } else if (inputValue > maxStock) {
                    this.value = maxStock;
                }
                
                updateTotalPrice();
            });
        });
    }
    
    // 表单提交处理
    if (buyForm) {
        buyForm.addEventListener('submit', function(e) {
            // 检查库存
            const currentAmount = parseInt(amountInput.value);
            const maxStock = parseInt(stockText.textContent);
            
            if (currentAmount > maxStock) {
                e.preventDefault();
                document.querySelector('.modal-body').innerHTML = "{{ __('dujiaoka.prompt.inventory_shortage') }}";
                myModal.show();
                return false;
            }
            
            // 检查购买限制
            @if(($buy_limit_num ?? 0) > 0)
            if (currentAmount > {{ $buy_limit_num }}) {
                e.preventDefault();
                document.querySelector('.modal-body').innerHTML = "{{ __('dujiaoka.prompt.purchase_limit_exceeded') }}";
                myModal.show();
                return false;
            }
            @endif
            
            // 处理规格选择 - 确保使用当前激活的规格选择模式
            const isSelectMode = btnModeBtn && !btnModeBtn.classList.contains('active');
            
            if (isSelectMode && selectContainer) {
                // 如果当前是下拉模式，同步选择的规格ID到sub_id字段
                const selectedValue = selectContainer.value;
                
                // 查找现有的sub_id字段或创建新的
                let subIdInput = this.querySelector('input[name="sub_id"]');
                if (!subIdInput) {
                    subIdInput = document.createElement('input');
                    subIdInput.type = 'hidden';
                    subIdInput.name = 'sub_id';
                    this.appendChild(subIdInput);
                }
                
                // 设置sub_id值
                subIdInput.value = selectedValue;
            }
        });
    }
    
    // 初始化 - 确保默认选中第一个规格并更新价格/库存/总价
    if (specRadios.length > 0) {
        const firstRadio = specRadios[0];
        firstRadio.checked = true;
        updatePriceAndStock(firstRadio.dataset.price, firstRadio.dataset.stock);
        updateTotalPrice();
    } else {
        // 如果没有规格选择，也要初始化总价
        updateTotalPrice();
    }
    
    // 确保总价初始值正确
    updateTotalPrice();
});
</script>
@stop