@extends('neon.layouts.seo')
@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-4" style="max-width: 416px;">

            @if(dujiaoka_config_get('is_open_login') == \App\Models\BaseModel::STATUS_OPEN)
            <div class="card card-body sticky">
                <form id="login-form" action="{{ url('login') }}" method="post" novalidate>
                    @csrf
                    <!-- 邮箱输入 -->
                    <div class="form-group mb-3">
                        <label class="form-label">邮箱 <span class="text-danger">*</span></label>
                        <input type="email" name="email" class="form-control" placeholder="请输入邮箱" required>
                        <div class="invalid-feedback">请输入您的邮箱!</div>
                    </div>
                    
                    <!-- 密码输入 -->
                    <div class="form-group mb-3">
                        <label class="form-label">密码 <span class="text-danger">*</span></label>
                        <input type="password" name="password" class="form-control" placeholder="请输入密码" required>
                        <div class="invalid-feedback">请输入您的密码!</div>
                    </div>

                    <!-- 数学验证题 -->
                    @if(dujiaoka_config_get('is_openlogin_img_code') == \App\Models\Goods::STATUS_OPEN)
                    <div class="form-group mb-3">
                        <label class="form-label" id="math-question"></label>
                        <input type="text" name="math_answer" class="form-control" placeholder="输入结果">
                    </div>
                    <div class="form-group mb-3">
                        <button type="button" class="btn btn-secondary w-100" id="refresh">
                            <i class="mdi mdi-refresh mr-1"></i>换一个
                        </button>
                    </div>
                    @endif

                    <!-- 登录和注册按钮并排显示 -->
                    <div class="mt-4 d-flex justify-content-center" style="gap: 3mm;">
                        <button type="submit" class="btn btn-lg btn-dark w-100">
                            立即登录
                        </button>
                        <a class="btn btn-lg btn-dark w-100" href="{{ url('register') }}">
                            立即注册
                        </a>
                    </div>
                </form>
            </div>

            @else
            <div class="alert alert-warning text-center mt-4">
                登录功能关闭维护中……<a href="/">返回首页</a>
            </div>
            @endif
        </div>
    </div>
</div>
@stop
