@extends('neon.layouts.default')

@section('header')
<style>
/* 企业级账户设置页面样式 */
.account-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 40vh;
    position: relative;
    overflow: hidden;
}

.account-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.account-tabs {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 0.5rem;
    margin-bottom: 2rem;
}

.account-tab {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
}

.account-tab.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.account-tab:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.form-control-glass {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    padding: 1rem 1.5rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.form-control-glass:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    color: white;
}

.form-control-glass::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.breadcrumb-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem 1.5rem;
}

.user-avatar-large {
    width: 120px;
    height: 120px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    margin: 0 auto 2rem;
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.avatar-upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
}

.user-avatar-large:hover .avatar-upload-overlay {
    opacity: 1;
}

.security-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
}

.notification-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.form-switch .form-check-input {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.form-switch .form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}
</style>
@endsection

@section('content')
<!-- 企业级账户设置页面 -->
<section class="account-hero d-flex align-items-center">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold text-white mb-4">
                    <i class="fas fa-user-cog me-3"></i>账户设置
                </h1>
                <p class="lead text-white mb-0">
                    管理您的个人信息和安全设置
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="text-center text-white">
                    <h5 class="mb-1">{{ Auth::user()->email }}</h5>
                    <small>VIP会员</small>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb-glass mb-5" aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="/user" class="text-light text-decoration-none">
                    <i class="fas fa-user me-1"></i>用户中心
                </a>
            </li>
            <li class="breadcrumb-item active text-white" aria-current="page">账户设置</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="account-card">
                <!-- 标签页导航 -->
                <div class="account-tabs">
                    <div class="d-flex">
                        <button class="account-tab active" data-tab="profile">
                            <i class="fas fa-user me-2"></i>个人资料
                        </button>
                        <button class="account-tab" data-tab="security">
                            <i class="fas fa-lock me-2"></i>密码设置
                        </button>
                        <button class="account-tab" data-tab="notifications">
                            <i class="fas fa-bell me-2"></i>通知设置
                        </button>
                    </div>
                </div>

                <!-- 标签页内容 -->
                <div class="tab-content">
                    <!-- 个人资料标签页 -->
                    <div class="tab-pane active" id="profile">
                        <!-- 用户头像 -->
                        <div class="text-center mb-5">
                            <div class="user-avatar-large">
                                <i class="fas fa-user"></i>
                                <div class="avatar-upload-overlay">
                                    <i class="fas fa-camera fa-2x"></i>
                                </div>
                            </div>
                            <h5 class="text-white mb-2">{{ Auth::user()->email }}</h5>
                            <p class="text-light mb-0">VIP会员 · 注册于 {{ Auth::user()->created_at->format('Y年m月') }}</p>
                        </div>

                        <!-- 个人信息表单 -->
                        <form id="profile-form" action="{{ url('/user/update-profile') }}" method="post" class="needs-validation" novalidate>
                            @csrf
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-envelope me-2"></i>邮箱地址
                                    </label>
                                    <input type="email" class="form-control-glass" 
                                           value="{{ Auth::user()->email }}" readonly>
                                    <small class="text-light mt-1 d-block">
                                        <i class="fas fa-info-circle me-1"></i>
                                        邮箱地址不可修改
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-user me-2"></i>用户名
                                    </label>
                                    <input type="text" name="username" class="form-control-glass" 
                                           value="{{ Auth::user()->username ?? '' }}" 
                                           placeholder="请输入用户名">
                                </div>
                                <div class="col-12">
                                    <div class="d-flex gap-3">
                                        <button type="submit" class="btn btn-gradient">
                                            <i class="fas fa-save me-2"></i>保存更改
                                        </button>
                                        <button type="button" class="btn btn-outline-light">
                                            <i class="fas fa-times me-2"></i>取消
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 密码设置标签页 -->
                    <div class="tab-pane" id="security">
                        <h5 class="text-white mb-4">
                            <i class="fas fa-shield-alt me-2"></i>安全设置
                        </h5>

                        <!-- 修改密码 -->
                        <div class="security-item">
                            <h6 class="text-white mb-3">
                                <i class="fas fa-key me-2"></i>修改密码
                            </h6>
                            <form id="password-form" action="{{ url('/user/change-password') }}" method="post" class="needs-validation" novalidate>
                                @csrf
                                <div class="row g-3">
                                    <div class="col-12">
                                        <label class="form-label text-white mb-2">当前密码</label>
                                        <input type="password" name="current_password" class="form-control-glass"
                                               placeholder="请输入当前密码" required>
                                        <div class="invalid-feedback">请输入当前密码!</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label text-white mb-2">新密码</label>
                                        <input type="password" name="new_password" id="newPassword" class="form-control-glass"
                                               placeholder="请输入新密码" required minlength="6">
                                        <div class="invalid-feedback">密码至少需要6位字符!</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label text-white mb-2">确认新密码</label>
                                        <input type="password" name="confirm_password" class="form-control-glass"
                                               placeholder="请再次输入新密码" required>
                                        <div class="invalid-feedback">两次输入的密码不一致!</div>
                                    </div>
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-gradient">
                                            <i class="fas fa-lock me-2"></i>更新密码
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- 安全信息 -->
                        <div class="security-item">
                            <h6 class="text-white mb-3">
                                <i class="fas fa-info-circle me-2"></i>安全信息
                            </h6>
                            <div class="text-light">
                                <div class="d-flex justify-content-between mb-2">
                                    <span>最后登录时间：</span>
                                    <span>{{ Auth::user()->last_login_at ?? '首次登录' }}</span>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>注册时间：</span>
                                    <span>{{ Auth::user()->created_at->format('Y-m-d H:i') }}</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>账户状态：</span>
                                    <span class="badge bg-success">正常</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 通知设置标签页 -->
                    <div class="tab-pane" id="notifications">
                        <h5 class="text-white mb-4">
                            <i class="fas fa-bell me-2"></i>通知设置
                        </h5>

                        <!-- 邮件通知 -->
                        <div class="notification-item">
                            <div>
                                <h6 class="text-white mb-1">邮件通知</h6>
                                <small class="text-light">接收订单状态更新和重要通知</small>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                            </div>
                        </div>

                        <!-- 订单通知 -->
                        <div class="notification-item">
                            <div>
                                <h6 class="text-white mb-1">订单通知</h6>
                                <small class="text-light">订单完成时发送通知邮件</small>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="orderNotifications" checked>
                            </div>
                        </div>

                        <!-- 促销通知 -->
                        <div class="notification-item">
                            <div>
                                <h6 class="text-white mb-1">促销通知</h6>
                                <small class="text-light">接收优惠活动和新品推荐</small>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="promotionNotifications">
                            </div>
                        </div>

                        <!-- 安全通知 -->
                        <div class="notification-item">
                            <div>
                                <h6 class="text-white mb-1">安全通知</h6>
                                <small class="text-light">账户安全相关的重要通知</small>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="securityNotifications" checked disabled>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="button" class="btn btn-gradient" onclick="saveNotificationSettings()">
                                <i class="fas fa-save me-2"></i>保存设置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 标签页切换功能
    const tabs = document.querySelectorAll('.account-tab');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // 移除所有活动状态
            tabs.forEach(t => t.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });

    // 个人资料表单提交
    const profileForm = document.getElementById('profile-form');
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');

            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>保存中...';

            // 这里可以添加AJAX提交逻辑
            setTimeout(() => {
                showToast('个人资料更新成功', 'success');
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>保存更改';
            }, 1000);
        });
    }

    // 密码表单提交
    const passwordForm = document.getElementById('password-form');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.querySelector('input[name="confirm_password"]').value;

            if (newPassword !== confirmPassword) {
                showToast('两次输入的密码不一致', 'error');
                return;
            }

            const submitBtn = this.querySelector('button[type="submit"]');

            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>更新中...';

            // 这里可以添加AJAX提交逻辑
            setTimeout(() => {
                showToast('密码更新成功', 'success');
                this.reset();
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-lock me-2"></i>更新密码';
            }, 1000);
        });
    }

    // 保存通知设置
    window.saveNotificationSettings = function() {
        const settings = {
            email: document.getElementById('emailNotifications').checked,
            order: document.getElementById('orderNotifications').checked,
            promotion: document.getElementById('promotionNotifications').checked,
            security: document.getElementById('securityNotifications').checked
        };

        // 这里可以添加AJAX保存逻辑
        showToast('通知设置已保存', 'success');
        console.log('Notification settings:', settings);
    };

    // 头像上传功能
    document.querySelector('.avatar-upload-overlay').addEventListener('click', function() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                // 这里可以添加头像上传逻辑
                showToast('头像上传功能开发中', 'info');
            }
        };
        input.click();
    });

    console.log('Enterprise Account Settings Page initialized successfully');
});
</script>
@endsection
