@extends('neon.layouts.default')

@section('header')
<style>
/* 企业级购买页面样式 */
.product-gallery {
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
}

.product-gallery img {
    transition: transform 0.3s ease;
}

.product-gallery:hover img {
    transform: scale(1.05);
}

.product-info-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
}

.spec-button {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.spec-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.spec-button.active {
    background: var(--primary-gradient);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.purchase-form {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
}

.form-control-glass {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    backdrop-filter: blur(10px);
}

.form-control-glass:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    color: white;
}

.form-control-glass::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.breadcrumb-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem 1.5rem;
}

.breadcrumb-glass .breadcrumb {
    margin-bottom: 0;
}

.breadcrumb-glass .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-glass .breadcrumb-item a:hover {
    color: white;
}

.breadcrumb-glass .breadcrumb-item.active {
    color: rgba(255, 255, 255, 0.6);
}

.price-display {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.quantity-selector {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.quantity-btn {
    background: none;
    border: none;
    color: white;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.quantity-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.quantity-input {
    background: none;
    border: none;
    color: white;
    text-align: center;
    width: 60px;
    padding: 0.75rem 0;
}

.payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.payment-method {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.payment-method.selected {
    background: var(--primary-gradient);
    border-color: rgba(255, 255, 255, 0.3);
}

.security-badge {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
}
</style>
@endsection

@section('content')
<!-- 企业级购买页面 -->
<div class="container py-5">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb-glass mb-5" aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}">
                    <i class="fas fa-shopping-bag me-1"></i>商品
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">{{ $gd_name }}</li>
        </ol>
    </nav>

    <div class="row g-5">
        <!-- 左侧：商品展示 -->
        <div class="col-lg-6">
            <!-- 商品图片 -->
            <div class="product-gallery mb-4">
                <img src="{{ picture_url($picture) }}"
                     alt="{{ $gd_name }}"
                     class="w-100"
                     style="height: 400px; object-fit: cover;">
                <div class="position-absolute top-0 end-0 p-3">
                    <button class="btn btn-outline-light btn-sm" onclick="openImageModal()">
                        <i class="fas fa-expand me-1"></i>查看大图
                    </button>
                </div>
            </div>

            <!-- 商品信息卡片 -->
            <div class="product-info-card text-white">
                <h1 class="gradient-text h2 mb-4">{{ $gd_name }}</h1>

                @if(!empty($gd_description))
                <div class="mb-4">
                    <h6 class="text-white mb-3">
                        <i class="fas fa-info-circle me-2"></i>商品描述
                    </h6>
                    <p class="text-light">{{ $gd_description }}</p>
                </div>
                @endif

                <!-- 商品特性 -->
                <div class="row g-3 mb-4">
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <div class="feature-icon me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                @if($type == \App\Models\Goods::AUTOMATIC_DELIVERY)
                                    <i class="fas fa-bolt"></i>
                                @else
                                    <i class="fas fa-user"></i>
                                @endif
                            </div>
                            <div>
                                <h6 class="text-white mb-1">发货方式</h6>
                                <p class="text-light mb-0 small">
                                    @if($type == \App\Models\Goods::AUTOMATIC_DELIVERY)
                                        自动发货
                                    @else
                                        人工发货
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <div class="feature-icon me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div>
                                <h6 class="text-white mb-1">安全保障</h6>
                                <p class="text-light mb-0 small">100%正品保证</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 库存信息 -->
                <div class="security-badge">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-success">
                            <i class="fas fa-check-circle me-2"></i>库存充足
                        </span>
                        <span class="text-success fw-bold">{{ $in_stock }} 件</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧：购买表单 -->
        <div class="col-lg-6">
            <div class="purchase-form text-white">
                <h3 class="gradient-text mb-4">
                    <i class="fas fa-shopping-cart me-2"></i>立即购买
                </h3>
                <!-- 购买表单 -->
                <form class="needs-validation" novalidate id="buy-form" action="{{ url('create-order') }}" method="post">
                    @csrf
                    <input type="hidden" name="gid" value="{{ $id }}">

                    <!-- 价格显示 -->
                    <div class="price-display mb-4">
                        <h4 class="mb-2">
                            <i class="fas fa-tag me-2"></i>商品价格
                        </h4>
                        <div class="h2 mb-0">
                            ¥<span id="actualPrice">{{ $price }}</span>
                        </div>
                        @if(!empty($wholesale_price_cnf) && is_array($wholesale_price_cnf))
                            <small class="d-block mt-2">
                                <i class="fas fa-gift me-1"></i>支持批发优惠
                            </small>
                        @endif
                    </div>

                    <!-- 多规格选择 -->
                    @if (!empty($goods_sub))
                    <div class="mb-4">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-cogs me-2"></i>选择规格
                        </h6>

                        <!-- 规格切换按钮 -->
                        <div class="d-flex gap-2 mb-3">
                            <button type="button" class="btn btn-outline-light btn-sm active" id="specBtnMode">
                                <i class="fas fa-th me-1"></i>按钮模式
                            </button>
                            <button type="button" class="btn btn-outline-light btn-sm" id="specSelectMode">
                                <i class="fas fa-list me-1"></i>下拉模式
                            </button>
                        </div>

                        <!-- 按钮模式 -->
                        <div id="btnModeContainer">
                            <div class="row g-2">
                                @foreach ($goods_sub as $ki=>$sub)
                                <div class="col-12">
                                    <input type="radio" class="btn-check spec-radio" name="sub_id" id="subid-{{ $sub['id'] }}"
                                           value="{{ $sub['id'] }}" data-price="{{ $sub['price'] }}" data-stock="{{ $sub['stock'] }}" @if($ki==0) checked @endif>
                                    <label class="spec-button w-100 text-white d-flex justify-content-between align-items-center" for="subid-{{ $sub['id'] }}">
                                        <span>{{ $sub['name'] }}</span>
                                        <span class="fw-bold">¥{{ number_format($sub['price'],2) }}</span>
                                    </label>
                                </div>
                                @endforeach
                            </div>
                        </div>

                        <!-- 下拉模式 -->
                        <select class="form-control-glass d-none" id="selectModeContainer" name="sub_id_select">
                            @foreach ($goods_sub as $ki=>$sub)
                            <option value="{{ $sub['id'] }}" data-price="{{ $sub['price'] }}" data-stock="{{ $sub['stock'] }}" @if($ki==0) selected @endif>
                                {{ $sub['name'] }} (¥{{ number_format($sub['price'],2) }})
                            </option>
                            @endforeach
                        </select>
                    </div>
                    @endif

                    <!-- 购买数量 -->
                    <div class="mb-4">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-shopping-cart me-2"></i>购买数量
                        </h6>
                        <div class="quantity-selector">
                            <button type="button" class="quantity-btn" onclick="decreaseQuantity()">
                                <i class="fas fa-minus"></i>
                            </button>
                            <input type="number" class="quantity-input" id="buy_amount" name="buy_amount" value="1" min="1" max="{{ $stock }}">
                            <button type="button" class="quantity-btn" onclick="increaseQuantity()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <small class="text-light mt-2 d-block">
                            <i class="fas fa-info-circle me-1"></i>
                            库存：<span id="stockText">{{ $stock }}</span> 件
                        </small>
                    </div>

                    <!-- 邮箱输入 -->
                    <div class="mb-4">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-envelope me-2"></i>接收邮箱
                        </h6>
                        <input type="email" class="form-control-glass" id="email" name="email"
                               placeholder="请输入您的邮箱地址" required
                               value="{{ Auth::check() ? Auth::user()->email : '' }}">
                        <small class="text-light mt-2 d-block">
                            <i class="fas fa-shield-alt me-1"></i>
                            商品将自动发送到此邮箱，请确保邮箱地址正确
                        </small>
                    </div>
                    <!-- 查询密码 -->
                    @if(dujiaoka_config_get('is_open_search_pwd') == \App\Models\Goods::STATUS_OPEN)
                    <div class="mb-4">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-key me-2"></i>查询密码
                        </h6>
                        <input type="text" class="form-control-glass" id="search_pwd" name="search_pwd"
                               placeholder="用于查询订单状态" required>
                        <small class="text-light mt-2 d-block">
                            <i class="fas fa-info-circle me-1"></i>
                            请记住此密码，查询订单时需要使用
                        </small>
                    </div>
                    @endif

                    <!-- 其他表单字段 -->
                    @if(($type ?? ($goods['type'] ?? null)) == \App\Models\Goods::MANUAL_PROCESSING && !empty($other_ipu) && is_array($other_ipu))
                    <div class="mb-4">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-edit me-2"></i>其他信息
                        </h6>
                        @foreach($other_ipu as $ipu)
                        <div class="mb-3">
                            <label for="{{ $ipu['field'] }}" class="text-white mb-2">
                                {{ $ipu['desc'] }}
                                @if($ipu['rule'] !== false)
                                    <span class="text-danger">*</span>
                                @endif
                            </label>
                            <input type="text"
                                   class="form-control-glass"
                                   id="{{ $ipu['field'] }}"
                                   name="{{ $ipu['field'] }}"
                                   @if($ipu['rule'] !== false) required @endif
                                   placeholder="请输入{{ $ipu['desc'] }}">
                        </div>
                        @endforeach
                    </div>
                    @endif

                    <!-- 优惠券 -->
                    @if(isset($open_coupon))
                    <div class="mb-4">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-ticket-alt me-2"></i>优惠券
                        </h6>
                        <div class="input-group">
                            <input type="text" class="form-control-glass" id="coupon" name="coupon_code"
                                   placeholder="输入优惠券代码">
                            <button type="button" class="btn btn-outline-light" onclick="applyCoupon()">
                                <i class="fas fa-check me-1"></i>使用
                            </button>
                        </div>
                        <small class="text-light mt-2 d-block">
                            <i class="fas fa-gift me-1"></i>
                            输入有效优惠券可享受折扣
                        </small>
                    </div>
                    @endif

                    <!-- 图片验证码 -->
                    @if(dujiaoka_config_get('is_open_img_code') == \App\Models\Goods::STATUS_OPEN)
                    <div class="mb-4">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-shield-alt me-2"></i>安全验证
                        </h6>
                        <div class="row g-3">
                            <div class="col-8">
                                <input type="text" name="img_verify_code" class="form-control-glass"
                                       id="verifyCode" placeholder="输入验证码" required>
                            </div>
                            <div class="col-4">
                                <img src="{{ captcha_src('buy') . time() }}"
                                     class="w-100 rounded cursor-pointer"
                                     style="height: 48px; object-fit: cover;"
                                     alt="验证码"
                                     onclick="refreshCaptcha()"
                                     id="imageCode"
                                     title="点击刷新验证码">
                            </div>
                        </div>
                        <small class="text-light mt-2 d-block">
                            <i class="fas fa-info-circle me-1"></i>
                            点击图片可刷新验证码
                        </small>
                    </div>
                    @endif

                    <!-- 支付方式 -->
                    <div class="mb-4">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-credit-card me-2"></i>选择支付方式
                        </h6>
                        <div class="payment-methods">
                            @foreach($payways as $index => $way)
                            <div class="payment-method {{ $index == 0 ? 'selected' : '' }}" data-type="{{ $way['pay_check'] }}" data-id="{{ $way['id'] }}">
                                <input type="radio" class="d-none" name="payway" value="{{ $way['id'] }}" id="payway-{{ $way['id'] }}" @if($index == 0) checked @endif>
                                <div class="text-center">
                                    <div class="mb-2">
                                        <i class="fas fa-credit-card fa-2x"></i>
                                    </div>
                                    <h6 class="mb-0 text-white">{{ $way['pay_name'] }}</h6>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>

                    <!-- 总价显示 -->
                    <div class="price-display mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="h5 mb-0">总计金额：</span>
                            <span class="h3 mb-0">
                                ¥<span id="totalPrice">{{ $price }}</span>
                            </span>
                        </div>
                    </div>

                    <!-- 购买按钮 -->
                    <input type="hidden" name="aff" value="">
                    <button type="submit" id="submitBtn" class="btn btn-gradient w-100 py-4 fw-bold fs-5">
                        <i class="fas fa-shopping-cart me-2"></i>
                        立即购买
                    </button>

                    <!-- 安全提示 -->
                    <div class="security-badge mt-4">
                        <div class="row g-3 text-center">
                            <div class="col-4">
                                <i class="fas fa-shield-alt text-success fa-2x mb-2"></i>
                                <p class="small mb-0 text-success">安全支付</p>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-bolt text-warning fa-2x mb-2"></i>
                                <p class="small mb-0 text-warning">极速发货</p>
                            </div>
                            <div class="col-4">
                                <i class="fas fa-headset text-info fa-2x mb-2"></i>
                                <p class="small mb-0 text-info">24/7客服</p>
                            </div>
                        </div>
                    </div>

                    <!-- 重要提示 -->
                    <div class="mt-4">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>重要提示
                        </h6>
                        <ul class="list-unstyled text-light small">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                支付完成后商品将自动发送到您的邮箱
                            </li>
                            @if(($type ?? ($goods['type'] ?? null)) == \App\Models\Goods::MANUAL_PROCESSING)
                            <li class="mb-2">
                                <i class="fas fa-clock text-warning me-2"></i>
                                人工发货商品请在工作时间联系客服
                            </li>
                            @endif
                            <li class="mb-2">
                                <i class="fas fa-envelope text-info me-2"></i>
                                请确保邮箱地址正确，避免无法接收商品
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-shield-alt text-primary me-2"></i>
                                如有问题请及时联系客服处理
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 商品详情标签页 -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="glass-card p-4">
                <ul class="nav nav-pills justify-content-center mb-4" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="description-tab" data-bs-toggle="tab"
                                data-bs-target="#description-tab-pane" type="button" role="tab">
                            <i class="fas fa-info-circle me-2"></i>商品详情
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="prompt-tab" data-bs-toggle="tab"
                                data-bs-target="#prompt-tab-pane" type="button" role="tab">
                            <i class="fas fa-exclamation-triangle me-2"></i>购买提示
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="tutorial-tab" data-bs-toggle="tab"
                                data-bs-target="#tutorial-tab-pane" type="button" role="tab">
                            <i class="fas fa-book me-2"></i>使用教程
                        </button>
                    </li>
                </ul>

                <div class="tab-content">
                    <div class="tab-pane fade show active" id="description-tab-pane" role="tabpanel">
                        <div class="text-white">
                            @if(!empty($description))
                                {!! $description !!}
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无商品详情</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="tab-pane fade" id="prompt-tab-pane" role="tabpanel">
                        <div class="text-white">
                            @if(!empty($buy_prompt))
                                {!! $buy_prompt !!}
                            @else
                                <div class="text-center py-5">
                                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">暂无购买提示</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <div class="tab-pane fade" id="tutorial-tab-pane" role="tabpanel">
                        <div class="text-white">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="glass-card p-4">
                                        <h6 class="text-white mb-3">
                                            <i class="fas fa-shopping-cart me-2"></i>购买流程
                                        </h6>
                                        <ol class="text-light">
                                            <li>选择商品规格和数量</li>
                                            <li>填写接收邮箱</li>
                                            <li>选择支付方式</li>
                                            <li>完成支付</li>
                                            <li>查收邮箱获取商品</li>
                                        </ol>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="glass-card p-4">
                                        <h6 class="text-white mb-3">
                                            <i class="fas fa-question-circle me-2"></i>常见问题
                                        </h6>
                                        <ul class="text-light">
                                            <li>支付后多久发货？</li>
                                            <li>如何查询订单状态？</li>
                                            <li>商品有问题怎么办？</li>
                                            <li>如何联系客服？</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图片查看模态框 -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content glass-card border-0">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white">
                    <i class="fas fa-image me-2"></i>商品图片
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img src="{{ picture_url($picture) }}" alt="{{ $gd_name }}" class="img-fluid rounded">
            </div>
        </div>
    </div>
</div>

<!-- 购买提示模态框 -->
<div class="modal fade" id="promptModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content glass-card border-0">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white">
                    <i class="fas fa-info-circle me-2"></i>购买提示
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-white">
                {!! $buy_prompt ?? '暂无购买提示' !!}
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn btn-gradient" data-bs-dismiss="modal">
                    <i class="fas fa-check me-1"></i>我知道了
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化模态框
    const imageModal = new bootstrap.Modal(document.getElementById('imageModal'));
    const promptModal = new bootstrap.Modal(document.getElementById('promptModal'));

    // 如果有购买提示，显示模态框
    @if(!empty($buy_prompt))
    promptModal.show();
    @endif

    // 获取DOM元素
    const btnModeBtn = document.getElementById('specBtnMode');
    const selectModeBtn = document.getElementById('specSelectMode');
    const btnContainer = document.getElementById('btnModeContainer');
    const selectContainer = document.getElementById('selectModeContainer');
    const buyForm = document.getElementById('buy-form');
    const amountInput = document.getElementById('buy_amount');
    const submitBtn = document.getElementById('submitBtn');
    const stockText = document.getElementById('stockText');
    const actualPrice = document.getElementById('actualPrice');
    const totalPrice = document.getElementById('totalPrice');

    // 全局函数
    window.openImageModal = function() {
        imageModal.show();
    };

    window.refreshCaptcha = function() {
        const img = document.getElementById('imageCode');
        if (img) {
            img.src = '{{ captcha_src('buy') }}' + Math.random();
        }
    };

    window.applyCoupon = function() {
        const couponInput = document.getElementById('coupon');
        const couponCode = couponInput.value.trim();

        if (!couponCode) {
            showToast('请输入优惠券代码', 'warning');
            return;
        }

        // 这里可以添加优惠券验证逻辑
        showToast('优惠券验证中...', 'info');

        // 模拟验证过程
        setTimeout(() => {
            showToast('优惠券验证成功！', 'success');
            updateTotalPrice(); // 重新计算价格
        }, 1000);
    };

    window.decreaseQuantity = function() {
        const current = parseInt(amountInput.value) || 1;
        if (current > 1) {
            amountInput.value = current - 1;
            updateTotalPrice();
        }
    };

    window.increaseQuantity = function() {
        const current = parseInt(amountInput.value) || 1;
        const maxStock = parseInt(stockText.textContent) || 999;
        if (current < maxStock) {
            amountInput.value = current + 1;
            updateTotalPrice();
        }
    };

    // 计算并更新总价
    function updateTotalPrice() {
        const price = parseFloat(actualPrice.textContent) || 0;
        const amount = parseInt(amountInput.value) || 1;
        const total = (price * amount).toFixed(2);
        totalPrice.textContent = total;
    }

    // 规格模式切换功能
    if (btnModeBtn && selectModeBtn) {
        btnModeBtn.addEventListener('click', function() {
            btnModeBtn.classList.add('active');
            selectModeBtn.classList.remove('active');
            btnContainer.classList.remove('d-none');
            selectContainer.classList.add('d-none');
        });

        selectModeBtn.addEventListener('click', function() {
            btnModeBtn.classList.remove('active');
            selectModeBtn.classList.add('active');
            btnContainer.classList.add('d-none');
            selectContainer.classList.remove('d-none');
        });
    }

    // 支付方式选择
    const paymentMethods = document.querySelectorAll('.payment-method');
    paymentMethods.forEach(method => {
        method.addEventListener('click', function() {
            // 移除所有选中状态
            paymentMethods.forEach(m => m.classList.remove('selected'));

            // 添加选中状态
            this.classList.add('selected');

            // 选中对应的radio
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
            }
        });
    });

    // 按钮模式规格选择
    const specRadios = document.querySelectorAll('.spec-radio');
    specRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                // 更新价格和库存显示
                updatePriceAndStock(this.dataset.price, this.dataset.stock);

                // 更新规格按钮样式
                document.querySelectorAll('.spec-button').forEach(btn => btn.classList.remove('active'));
                document.querySelector(`label[for="${this.id}"]`).classList.add('active');

                // 同步下拉框选择
                if (selectContainer) {
                    selectContainer.value = this.value;
                }

                // 更新总价
                updateTotalPrice();
            }
        });
    });

    // 下拉模式规格选择
    if (selectContainer) {
        selectContainer.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const price = selectedOption.dataset.price;
            const stock = selectedOption.dataset.stock;
            const value = selectedOption.value;

            // 更新价格和库存显示
            updatePriceAndStock(price, stock);

            // 同步单选按钮选择
            const radio = document.getElementById('subid-' + value);
            if (radio) {
                radio.checked = true;
            }

            // 更新总价
            updateTotalPrice();
        });
    }

    // 更新价格和库存显示
    function updatePriceAndStock(price, stock) {
        if (actualPrice) actualPrice.textContent = parseFloat(price).toFixed(2);
        if (stockText) stockText.textContent = stock;

        // 更新数量输入框的最大值
        if (amountInput) {
            amountInput.max = stock;

            // 如果当前数量超过库存，调整为库存数量
            if (parseInt(amountInput.value) > parseInt(stock)) {
                amountInput.value = stock;
            }
        }
    }

    // 数量输入框事件监听
    if (amountInput) {
        ['change', 'input', 'keyup', 'blur'].forEach(function(eventType) {
            amountInput.addEventListener(eventType, function() {
                const maxStock = parseInt(stockText.textContent) || 999;
                const inputValue = parseInt(this.value) || 1;

                if (inputValue < 1) {
                    this.value = 1;
                } else if (inputValue > maxStock) {
                    this.value = maxStock;
                    showToast('购买数量不能超过库存', 'warning');
                }

                updateTotalPrice();
            });
        });
    }

    // 表单提交处理
    if (buyForm) {
        buyForm.addEventListener('submit', function(e) {
            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>处理中...';

            // 检查必填字段
            const email = document.getElementById('email');
            if (email && !email.value.trim()) {
                e.preventDefault();
                showToast('请输入邮箱地址', 'error');
                resetSubmitButton();
                return false;
            }

            // 检查邮箱格式
            if (email && !isValidEmail(email.value)) {
                e.preventDefault();
                showToast('请输入有效的邮箱地址', 'error');
                resetSubmitButton();
                return false;
            }

            // 检查库存
            const currentAmount = parseInt(amountInput.value) || 1;
            const maxStock = parseInt(stockText.textContent) || 0;

            if (currentAmount > maxStock) {
                e.preventDefault();
                showToast('购买数量超过库存', 'error');
                resetSubmitButton();
                return false;
            }

            // 检查购买限制
            @if(($buy_limit_num ?? 0) > 0)
            if (currentAmount > {{ $buy_limit_num }}) {
                e.preventDefault();
                showToast('购买数量超过限制', 'error');
                resetSubmitButton();
                return false;
            }
            @endif

            // 检查支付方式
            const selectedPayment = document.querySelector('input[name="payway"]:checked');
            if (!selectedPayment) {
                e.preventDefault();
                showToast('请选择支付方式', 'error');
                resetSubmitButton();
                return false;
            }

            // 处理规格选择
            const isSelectMode = btnModeBtn && !btnModeBtn.classList.contains('active');
            if (isSelectMode && selectContainer) {
                const selectedValue = selectContainer.value;
                let subIdInput = this.querySelector('input[name="sub_id"]');
                if (!subIdInput) {
                    subIdInput = document.createElement('input');
                    subIdInput.type = 'hidden';
                    subIdInput.name = 'sub_id';
                    this.appendChild(subIdInput);
                }
                subIdInput.value = selectedValue;
            }

            // 如果所有验证通过，显示成功消息
            showToast('正在创建订单...', 'info');
        });
    }

    // 重置提交按钮状态
    function resetSubmitButton() {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-shopping-cart me-2"></i>立即购买';
    }

    // 邮箱验证函数
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // 初始化
    if (specRadios.length > 0) {
        const firstRadio = specRadios[0];
        firstRadio.checked = true;
        updatePriceAndStock(firstRadio.dataset.price, firstRadio.dataset.stock);
        document.querySelector(`label[for="${firstRadio.id}"]`).classList.add('active');
    }

    // 确保总价初始值正确
    updateTotalPrice();

    console.log('Enterprise Buy Page initialized successfully');
});
</script>
@endsection