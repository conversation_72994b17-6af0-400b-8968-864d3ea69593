@extends('neon.layouts.default')

@section('header')
<style>
/* 企业级支付页面样式 */
.payment-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 40vh;
    position: relative;
    overflow: hidden;
}

.payment-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.order-summary-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 2rem;
}

.product-item {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.08);
    margin-bottom: 1rem;
}

.price-breakdown {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.08);
}

.total-price {
    background: var(--primary-gradient);
    border-radius: 12px;
    padding: 1rem;
    color: white;
    font-weight: bold;
}

.countdown-timer {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border-radius: 12px;
    padding: 1rem;
    color: white;
    text-align: center;
    margin-bottom: 1rem;
}

.security-badge {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    color: #28a745;
    font-size: 0.9rem;
}

.breadcrumb-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem 1.5rem;
}
</style>
@endsection

@section('content')
<!-- 企业级支付页面 -->
<section class="payment-hero d-flex align-items-center">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold text-white mb-4">
                    <i class="fas fa-shopping-cart me-3"></i>确认订单
                </h1>
                <p class="lead text-white mb-0">
                    请确认您的订单信息并选择支付方式
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="security-badge">
                    <i class="fas fa-shield-alt me-2"></i>SSL安全支付
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb-glass mb-5" aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-shopping-bag me-1"></i>购物
                </a>
            </li>
            <li class="breadcrumb-item active text-white" aria-current="page">确认订单</li>
        </ol>
    </nav>

    <div class="row g-4">
        <!-- 左侧：订单详情 -->
        <div class="col-lg-8">
            <div class="payment-card">
                <!-- 订单倒计时 -->
                <div class="countdown-timer">
                    <h6 class="mb-2">
                        <i class="fas fa-clock me-2"></i>订单有效期
                    </h6>
                    <div id="countdown" class="h5 mb-0">
                        剩余时间：<span id="timer">30:00</span>
                    </div>
                </div>

                <!-- 商品信息 -->
                <div class="product-item">
                    <h5 class="text-white mb-3">
                        <i class="fas fa-box me-2"></i>商品详情
                    </h5>

                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="text-white mb-2">{{ $title }}</h6>
                            <div class="text-light">
                                <p class="mb-1">
                                    <strong>购买数量：</strong>{{ $buy_amount }} 件
                                </p>
                                <p class="mb-1">
                                    <strong>订单编号：</strong>
                                    <span class="text-primary">{{ $order_sn }}</span>
                                    <button class="btn btn-sm btn-outline-light ms-2" onclick="copyOrderSN()" title="复制订单号">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </p>
                                @if(!empty($coupon))
                                <p class="mb-1">
                                    <strong>优惠券：</strong>
                                    <span class="text-success">{{ $coupon['coupon'] }}</span>
                                </p>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="h4 text-success mb-0">
                                ¥{{ number_format($actual_price, 2) }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 价格明细 -->
                <div class="price-breakdown mb-4">
                    <h6 class="text-white mb-3">
                        <i class="fas fa-calculator me-2"></i>价格明细
                    </h6>
                    <div class="text-light">
                        <div class="d-flex justify-content-between mb-2">
                            <span>商品单价：</span>
                            <span>¥{{ number_format($goods_price, 2) }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>购买数量：</span>
                            <span>{{ $buy_amount }} 件</span>
                        </div>
                        @if($wholesale_discount_price > 0)
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>批发优惠：</span>
                            <span>-¥{{ number_format($wholesale_discount_price, 2) }}</span>
                        </div>
                        @endif
                        @if(!empty($coupon))
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>优惠券折扣：</span>
                            <span>-¥{{ number_format($coupon_discount_price, 2) }}</span>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- 其他信息 -->
                @if(!empty($info))
                <div class="product-item">
                    <h6 class="text-white mb-3">
                        <i class="fas fa-info-circle me-2"></i>订单信息
                    </h6>
                    <div class="text-light">
                        {!! $info !!}
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- 右侧：订单汇总 -->
        <div class="col-lg-4">
            <div class="order-summary-card">
                <h5 class="text-white mb-4">
                    <i class="fas fa-file-invoice me-2"></i>订单汇总
                </h5>

                <!-- 订单基本信息 -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between mb-2 text-light">
                        <span>支付方式：</span>
                        <span class="text-white">{{ $pay['pay_name'] ?? '--' }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2 text-light">
                        <span>下单邮箱：</span>
                        <span class="text-white">{{ $email }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2 text-light">
                        <span>发货方式：</span>
                        @if($type == \App\Models\Order::AUTOMATIC_DELIVERY)
                            <span class="badge bg-success">
                                <i class="fas fa-bolt me-1"></i>自动发货
                            </span>
                        @else
                            <span class="badge bg-warning">
                                <i class="fas fa-user me-1"></i>人工发货
                            </span>
                        @endif
                    </div>
                    <div class="d-flex justify-content-between mb-2 text-light">
                        <span>创建时间：</span>
                        <span class="text-white">{{ $created_at }}</span>
                    </div>
                </div>

                <!-- 总价显示 -->
                <div class="total-price text-center mb-4">
                    <h6 class="mb-2">应付金额</h6>
                    <div class="h3 mb-0">¥{{ number_format($actual_price, 2) }}</div>
                </div>

                <!-- 支付按钮 -->
                <div class="d-grid gap-2">
                    <a class="btn btn-gradient btn-lg py-3"
                       href="{{ url('pay-gateway', [
                         'handle' => urlencode($pay['pay_handleroute']),
                         'payway' => $pay['pay_check'],
                         'orderSN' => $order_sn
                       ]) }}">
                        <i class="fas fa-credit-card me-2"></i>
                        立即支付 ¥{{ number_format($actual_price, 2) }}
                    </a>

                    <a href="{{ url('/') }}" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left me-2"></i>返回首页
                    </a>
                </div>

                <!-- 安全提示 -->
                <div class="mt-4 p-3 rounded" style="background: rgba(40, 167, 69, 0.1); border: 1px solid rgba(40, 167, 69, 0.3);">
                    <div class="d-flex align-items-center text-success">
                        <i class="fas fa-shield-alt fa-2x me-3"></i>
                        <div>
                            <h6 class="mb-1">安全保障</h6>
                            <small>256位SSL加密保护您的支付安全</small>
                        </div>
                    </div>
                </div>

                <!-- 支付说明 -->
                <div class="mt-4">
                    <h6 class="text-white mb-3">
                        <i class="fas fa-question-circle me-2"></i>支付说明
                    </h6>
                    <ul class="list-unstyled text-light small">
                        <li class="mb-2">
                            <i class="fas fa-check text-success me-2"></i>
                            支付完成后商品将自动发送到您的邮箱
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock text-warning me-2"></i>
                            订单有效期为30分钟，请及时完成支付
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-headset text-info me-2"></i>
                            如有问题请及时联系客服
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-history text-primary me-2"></i>
                            可通过订单号查询支付状态
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 移动端固定支付按钮 -->
<div class="fixed-bottom d-lg-none">
    <div class="container-fluid p-3" style="background: var(--glass-bg); backdrop-filter: blur(20px); border-top: 1px solid var(--glass-border);">
        <a class="btn btn-gradient w-100 py-3 fw-bold"
           href="{{ url('pay-gateway', [
             'handle' => urlencode($pay['pay_handleroute']),
             'payway' => $pay['pay_check'],
             'orderSN' => $order_sn
           ]) }}">
            <i class="fas fa-credit-card me-2"></i>
            立即支付 ¥{{ number_format($actual_price, 2) }}
        </a>
    </div>
</div>
@endsection

@section('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 订单倒计时功能
    let timeLeft = 30 * 60; // 30分钟

    function updateCountdown() {
        if (timeLeft > 0) {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            document.getElementById('timer').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            timeLeft--;
        } else {
            document.getElementById('timer').textContent = '已过期';
            document.getElementById('countdown').innerHTML =
                '<span class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>订单已过期</span>';

            // 禁用支付按钮
            document.querySelectorAll('a[href*="pay-gateway"]').forEach(btn => {
                btn.classList.add('disabled');
                btn.style.pointerEvents = 'none';
                btn.innerHTML = '<i class="fas fa-times me-2"></i>订单已过期';
            });
        }
    }

    // 每秒更新倒计时
    updateCountdown();
    setInterval(updateCountdown, 1000);

    // 支付按钮点击效果
    document.querySelectorAll('a[href*="pay-gateway"]').forEach(btn => {
        btn.addEventListener('click', function(e) {
            if (!this.classList.contains('disabled')) {
                this.innerHTML = '<span class="loading-spinner me-2"></span>跳转支付中...';
            }
        });
    });

    // 复制订单号功能
    window.copyOrderSN = function() {
        const orderSN = '{{ $order_sn }}';
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(orderSN).then(() => {
                showToast('订单号已复制', 'success');
            });
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = orderSN;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showToast('订单号已复制', 'success');
            } catch (err) {
                showToast('复制失败，请手动复制', 'error');
            }

            document.body.removeChild(textArea);
        }
    };

    console.log('Enterprise Payment Page initialized successfully');
});
</script>
@endsection