@extends('neon.layouts.default')

@section('header')
<style>
/* 企业级文章页面样式 */
.article-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 40vh;
    position: relative;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.article-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.article-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.article-image {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-card:hover .article-image {
    transform: scale(1.1);
}

.category-nav {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem;
}

.category-link {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-block;
    margin: 0.25rem;
}

.category-link:hover {
    background: var(--primary-gradient);
    transform: translateY(-2px);
    color: white;
}

.category-link.active {
    background: var(--primary-gradient);
    color: white;
}

.article-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.article-excerpt {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.read-more-btn {
    background: var(--primary-gradient);
    border: none;
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.read-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.breadcrumb-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem 1.5rem;
}

.pagination-glass .page-link {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    color: white;
    margin: 0 0.25rem;
    border-radius: 8px;
}

.pagination-glass .page-link:hover {
    background: var(--primary-gradient);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
}

.pagination-glass .page-item.active .page-link {
    background: var(--primary-gradient);
    border-color: rgba(255, 255, 255, 0.3);
}
</style>
@endsection

@section('content')
<!-- 企业级文章页面 -->

<!-- 文章页面头部 -->
<section class="article-hero d-flex align-items-center">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold text-white mb-4">
                    <i class="fas fa-newspaper me-3"></i>资讯中心
                </h1>
                <p class="lead text-white mb-0">
                    获取最新的行业资讯、产品动态和技术分享
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex justify-content-lg-end gap-3">
                    <div class="text-center text-white">
                        <h3 class="mb-1">{{ $articles->total() }}</h3>
                        <small>篇文章</small>
                    </div>
                    <div class="text-center text-white">
                        <h3 class="mb-1">{{ count($categorys) }}</h3>
                        <small>个分类</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb-glass mb-5" aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </li>
            <li class="breadcrumb-item active text-white" aria-current="page">资讯中心</li>
        </ol>
    </nav>

    <!-- 分类导航 -->
    <div class="category-nav mb-5">
        <div class="d-flex flex-wrap justify-content-center">
            <a href="/article" class="category-link {{ empty($catId) ? 'active' : '' }}">
                <i class="fas fa-th-large me-2"></i>全部分类
            </a>
            @foreach($categorys as $category)
                <a href="?cat_id={{ $category['id'] }}" class="category-link {{ $category['id'] == $catId ? 'active' : '' }}">
                    <i class="fas fa-folder me-2"></i>{{ $category['category_name'] }}
                </a>
            @endforeach
        </div>
    </div>
    <!-- 文章列表 -->
    <div class="row g-4">
        @if($articles->count() > 0)
            @foreach($articles as $article)
            <div class="col-lg-6 col-xl-4">
                <article class="article-card h-100">
                    <!-- 文章图片 -->
                    @if($article['picture'])
                    <div class="position-relative overflow-hidden">
                        <img src="{{ picture_url($article['picture']) }}"
                             alt="{{ $article['title'] }}"
                             class="article-image w-100">
                        <div class="position-absolute top-0 end-0 p-3">
                            <span class="badge bg-primary">
                                <i class="fas fa-eye me-1"></i>{{ $article['views'] ?? 0 }}
                            </span>
                        </div>
                    </div>
                    @endif

                    <!-- 文章内容 -->
                    <div class="p-4">
                        <!-- 文章分类 -->
                        @if($article['category'])
                        <div class="mb-3">
                            <span class="badge bg-secondary">
                                <i class="fas fa-folder me-1"></i>{{ $article['category']['category_name'] }}
                            </span>
                        </div>
                        @endif

                        <!-- 文章标题 -->
                        <h5 class="text-white mb-3">
                            <a href="article/{{ !empty($article['link']) ? $article['link'] : $article['id'] }}.html"
                               class="text-decoration-none text-white">
                                {{ $article['title'] }}
                            </a>
                        </h5>

                        <!-- 文章摘要 -->
                        <p class="article-excerpt mb-4">
                            {{ Str::limit(strip_tags($article['content']), 120, '...') }}
                        </p>

                        <!-- 文章元信息 -->
                        <div class="article-meta mb-4">
                            <span>
                                <i class="fas fa-calendar me-1"></i>
                                {{ $article['updated_at']->format('Y-m-d') }}
                            </span>
                            <span>
                                <i class="fas fa-user me-1"></i>
                                {{ $article['author'] ?? '管理员' }}
                            </span>
                        </div>

                        <!-- 阅读更多按钮 -->
                        <a href="article/{{ !empty($article['link']) ? $article['link'] : $article['id'] }}.html"
                           class="read-more-btn">
                            <span>阅读全文</span>
                            <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </article>
            </div>
            @endforeach
        @else
            <!-- 无文章状态 -->
            <div class="col-12">
                <div class="text-center py-5">
                    <div class="article-card p-5">
                        <i class="fas fa-newspaper fa-4x text-muted mb-4"></i>
                        <h4 class="text-white mb-3">暂无文章</h4>
                        <p class="text-light mb-4">该分类下暂时没有文章，请查看其他分类</p>
                        <a href="/article" class="btn btn-gradient">
                            <i class="fas fa-list me-2"></i>查看全部文章
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- 分页导航 -->
    @if($articles->hasPages())
    <div class="d-flex justify-content-center mt-5">
        <nav aria-label="文章分页">
            <ul class="pagination pagination-glass">
                {{-- 上一页 --}}
                @if ($articles->onFirstPage())
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-chevron-left"></i>
                        </span>
                    </li>
                @else
                    <li class="page-item">
                        <a class="page-link" href="{{ $articles->previousPageUrl() }}">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    </li>
                @endif

                {{-- 页码 --}}
                @foreach ($articles->getUrlRange(1, $articles->lastPage()) as $page => $url)
                    @if ($page == $articles->currentPage())
                        <li class="page-item active">
                            <span class="page-link">{{ $page }}</span>
                        </li>
                    @else
                        <li class="page-item">
                            <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                        </li>
                    @endif
                @endforeach

                {{-- 下一页 --}}
                @if ($articles->hasMorePages())
                    <li class="page-item">
                        <a class="page-link" href="{{ $articles->nextPageUrl() }}">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                @else
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    </li>
                @endif
            </ul>
        </nav>
    </div>
    @endif
</div>
@endsection

@section('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 文章卡片悬停效果增强
    document.querySelectorAll('.article-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // 分类链接点击效果
    document.querySelectorAll('.category-link').forEach(link => {
        link.addEventListener('click', function(e) {
            // 添加加载效果
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>加载中...';
        });
    });

    // 阅读更多按钮悬停效果
    document.querySelectorAll('.read-more-btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.querySelector('i').style.transform = 'translateX(5px)';
        });

        btn.addEventListener('mouseleave', function() {
            this.querySelector('i').style.transform = 'translateX(0)';
        });
    });

    console.log('Enterprise Article List Page initialized successfully');
});
</script>
@endsection
