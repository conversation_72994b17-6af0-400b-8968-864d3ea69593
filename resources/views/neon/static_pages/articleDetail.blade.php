@extends('neon.layouts.default')

@section('header')
<style>
/* 企业级文章详情页面样式 */
.article-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 50vh;
    position: relative;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.article-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 3rem;
}

.article-meta-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.breadcrumb-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem 1.5rem;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6 {
    color: white;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.article-content p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.8;
    margin-bottom: 1.5rem;
}

.article-content img {
    max-width: 100%;
    height: auto;
    border-radius: 12px;
    margin: 1.5rem 0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.article-content blockquote {
    background: rgba(102, 126, 234, 0.1);
    border-left: 4px solid #667eea;
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
    border-radius: 0 12px 12px 0;
    color: rgba(255, 255, 255, 0.9);
}

.article-content code {
    background: rgba(0, 0, 0, 0.3);
    color: #f8f8f2;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
}

.article-content pre {
    background: rgba(0, 0, 0, 0.5);
    color: #f8f8f2;
    padding: 1.5rem;
    border-radius: 12px;
    overflow-x: auto;
    margin: 1.5rem 0;
}

.article-content ul,
.article-content ol {
    color: rgba(255, 255, 255, 0.9);
    padding-left: 2rem;
    margin-bottom: 1.5rem;
}

.article-content li {
    margin-bottom: 0.5rem;
}

.article-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    overflow: hidden;
}

.article-content th,
.article-content td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
}

.article-content th {
    background: rgba(102, 126, 234, 0.2);
    font-weight: 600;
    color: white;
}

.share-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.share-btn {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.share-btn:hover {
    transform: translateY(-3px);
    color: white;
}

.share-btn.facebook:hover { background: #1877f2; }
.share-btn.twitter:hover { background: #1da1f2; }
.share-btn.linkedin:hover { background: #0077b5; }
.share-btn.wechat:hover { background: #07c160; }

.related-articles {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
}

.related-article-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.related-article-card:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.1);
}
</style>
@endsection

@section('content')
<!-- 企业级文章详情页面 -->

<!-- 文章头部 -->
<section class="article-hero d-flex align-items-center">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold text-white mb-4">
                    {{ $article['title'] }}
                </h1>
                <div class="d-flex flex-wrap gap-4 text-white">
                    <span>
                        <i class="fas fa-calendar me-2"></i>
                        {{ $article['updated_at']->format('Y年m月d日') }}
                    </span>
                    <span>
                        <i class="fas fa-user me-2"></i>
                        {{ $article['author'] ?? '管理员' }}
                    </span>
                    <span>
                        <i class="fas fa-folder me-2"></i>
                        {{ $article['category']['category_name'] ?? '未分类' }}
                    </span>
                    <span>
                        <i class="fas fa-eye me-2"></i>
                        {{ $article['views'] ?? 0 }} 次阅读
                    </span>
                </div>
            </div>
            <div class="col-lg-4 text-lg-end">
                @if($article['picture'])
                <img src="{{ picture_url($article['picture']) }}"
                     alt="{{ $article['title'] }}"
                     class="img-fluid rounded shadow-lg"
                     style="max-height: 200px; object-fit: cover;">
                @endif
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb-glass mb-5" aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="/article" class="text-light text-decoration-none">
                    <i class="fas fa-newspaper me-1"></i>资讯中心
                </a>
            </li>
            @if($article['category'])
            <li class="breadcrumb-item">
                <a href="/article?cat_id={{ $article['category_id'] }}" class="text-light text-decoration-none">
                    {{ $article['category']['category_name'] }}
                </a>
            </li>
            @endif
            <li class="breadcrumb-item active text-white" aria-current="page">
                {{ Str::limit($article['title'], 30) }}
            </li>
        </ol>
    </nav>

    <div class="row g-5">
        <!-- 文章内容 -->
        <div class="col-lg-8">
            <article class="article-content">
                <!-- 文章正文 -->
                <div class="article-body">
                    {!! $article['content'] !!}
                </div>

                <!-- 分享按钮 -->
                <div class="share-buttons">
                    <a href="#" class="share-btn facebook" title="分享到Facebook" onclick="shareToFacebook()">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="share-btn twitter" title="分享到Twitter" onclick="shareToTwitter()">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="share-btn linkedin" title="分享到LinkedIn" onclick="shareToLinkedIn()">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="#" class="share-btn wechat" title="分享到微信" onclick="shareToWechat()">
                        <i class="fab fa-weixin"></i>
                    </a>
                    <a href="#" class="share-btn" title="复制链接" onclick="copyLink()">
                        <i class="fas fa-link"></i>
                    </a>
                </div>
            </article>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 文章信息卡片 -->
            <div class="article-meta-card mb-4">
                <h6 class="text-white mb-3">
                    <i class="fas fa-info-circle me-2"></i>文章信息
                </h6>
                <div class="text-light">
                    <p class="mb-2">
                        <strong>发布时间：</strong>{{ $article['created_at']->format('Y-m-d H:i') }}
                    </p>
                    <p class="mb-2">
                        <strong>更新时间：</strong>{{ $article['updated_at']->format('Y-m-d H:i') }}
                    </p>
                    <p class="mb-2">
                        <strong>阅读次数：</strong>{{ $article['views'] ?? 0 }}
                    </p>
                    <p class="mb-0">
                        <strong>文章分类：</strong>{{ $article['category']['category_name'] ?? '未分类' }}
                    </p>
                </div>
            </div>

            <!-- 相关文章 -->
            @if(isset($relatedArticles) && count($relatedArticles) > 0)
            <div class="related-articles">
                <h6 class="text-white mb-4">
                    <i class="fas fa-newspaper me-2"></i>相关文章
                </h6>
                @foreach($relatedArticles as $related)
                <div class="related-article-card mb-3">
                    <h6 class="text-white mb-2">
                        <a href="article/{{ !empty($related['link']) ? $related['link'] : $related['id'] }}.html"
                           class="text-decoration-none text-white">
                            {{ Str::limit($related['title'], 40) }}
                        </a>
                    </h6>
                    <small class="text-light">
                        <i class="fas fa-calendar me-1"></i>
                        {{ $related['updated_at']->format('m-d') }}
                    </small>
                </div>
                @endforeach
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@section('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 分享功能
    window.shareToFacebook = function() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent(document.title);
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank', 'width=600,height=400');
    };

    window.shareToTwitter = function() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent(document.title);
        window.open(`https://twitter.com/intent/tweet?url=${url}&text=${title}`, '_blank', 'width=600,height=400');
    };

    window.shareToLinkedIn = function() {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent(document.title);
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank', 'width=600,height=400');
    };

    window.shareToWechat = function() {
        showToast('请复制链接后在微信中分享', 'info');
        copyLink();
    };

    window.copyLink = function() {
        const url = window.location.href;
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(url).then(() => {
                showToast('链接已复制到剪贴板', 'success');
            }).catch(() => {
                fallbackCopyTextToClipboard(url);
            });
        } else {
            fallbackCopyTextToClipboard(url);
        }
    };

    function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showToast('链接已复制到剪贴板', 'success');
            } else {
                showToast('复制失败，请手动复制', 'error');
            }
        } catch (err) {
            showToast('复制失败，请手动复制', 'error');
        }

        document.body.removeChild(textArea);
    }

    // 图片懒加载
    const images = document.querySelectorAll('.article-content img');
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.style.opacity = '0';
                    img.style.transition = 'opacity 0.3s ease';
                    img.onload = () => {
                        img.style.opacity = '1';
                    };
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }

    // 代码块复制功能
    document.querySelectorAll('pre').forEach(pre => {
        const button = document.createElement('button');
        button.className = 'btn btn-sm btn-outline-light position-absolute top-0 end-0 m-2';
        button.innerHTML = '<i class="fas fa-copy"></i>';
        button.onclick = () => {
            const code = pre.textContent;
            if (navigator.clipboard) {
                navigator.clipboard.writeText(code).then(() => {
                    showToast('代码已复制', 'success');
                });
            }
        };
        pre.style.position = 'relative';
        pre.appendChild(button);
    });

    console.log('Enterprise Article Detail Page initialized successfully');
});
</script>
@endsection