@extends('neon.layouts.default')

@section('header')
<style>
/* 企业级二维码支付页面样式 */
.qrpay-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 60vh;
    position: relative;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.qrpay-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    text-align: center;
}

.qr-container {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.qr-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%),
                linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 10px 10px;
    opacity: 0.1;
    pointer-events: none;
}

.qr-code {
    position: relative;
    z-index: 1;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.countdown-display {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border-radius: 15px;
    padding: 1.5rem;
    color: white;
    margin-bottom: 2rem;
}

.payment-amount {
    background: var(--primary-gradient);
    border-radius: 15px;
    padding: 1.5rem;
    color: white;
    margin: 2rem 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ffc107;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.app-download-btn {
    background: var(--primary-gradient);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.app-download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.payment-steps {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.step-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.9);
}

.step-number {
    width: 30px;
    height: 30px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 1rem;
    flex-shrink: 0;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: floatUpDown 8s ease-in-out infinite;
}

@keyframes floatUpDown {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

.breadcrumb-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem 1.5rem;
}
</style>
@endsection

@section('content')
<!-- 企业级二维码支付页面 -->

<!-- 支付页面头部 -->
<section class="qrpay-hero d-flex align-items-center">
    <!-- 浮动装饰元素 -->
    <div class="floating-elements">
        <div class="floating-element" style="left: 10%; top: 20%; font-size: 2rem;">
            <i class="fas fa-qrcode"></i>
        </div>
        <div class="floating-element" style="left: 80%; top: 30%; font-size: 1.5rem;">
            <i class="fas fa-mobile-alt"></i>
        </div>
        <div class="floating-element" style="left: 15%; top: 70%; font-size: 1.8rem;">
            <i class="fas fa-scan"></i>
        </div>
        <div class="floating-element" style="left: 75%; top: 80%; font-size: 1.3rem;">
            <i class="fas fa-credit-card"></i>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h1 class="display-4 fw-bold text-white mb-4">
                    <i class="fas fa-qrcode me-3"></i>扫码支付
                </h1>
                <p class="lead text-white mb-0">
                    请使用手机扫描二维码完成支付
                </p>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb-glass mb-5" aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-shopping-bag me-1"></i>购物
                </a>
            </li>
            <li class="breadcrumb-item active text-white" aria-current="page">扫码支付</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="qrpay-card">
                <!-- 倒计时显示 -->
                <div class="countdown-display">
                    <h5 class="mb-2">
                        <i class="fas fa-clock me-2"></i>订单有效期
                    </h5>
                    <div class="h4 mb-0">
                        剩余时间：<span id="countdown">{{ dujiaoka_config_get('order_expire_time', 5) }}:00</span>
                    </div>
                </div>

                <!-- 支付金额 -->
                <div class="payment-amount">
                    <h5 class="mb-2">支付金额</h5>
                    <div class="h2 mb-0">¥{{ $actual_price }}</div>
                </div>

                <!-- 二维码容器 -->
                <div class="qr-container">
                    <h6 class="text-dark mb-3">
                        <i class="fas fa-mobile-alt me-2"></i>请使用手机扫描二维码
                    </h6>
                    <img src="data:image/png;base64,{!! base64_encode(QrCode::format('png')->size(200)->generate($qr_code)) !!}"
                         alt="支付二维码" class="qr-code">
                </div>

                <!-- 支付状态 -->
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span class="text-white">等待支付中...</span>
                </div>

                <!-- 移动端直接支付按钮 -->
                @if(Agent::isMobile() && isset($jump_payuri))
                <a href="{{ $jump_payuri }}" class="app-download-btn">
                    <i class="fas fa-mobile-alt"></i>
                    <span>打开应用支付</span>
                </a>
                @endif

                <!-- 支付步骤说明 -->
                <div class="payment-steps">
                    <h6 class="text-white mb-3">
                        <i class="fas fa-list-ol me-2"></i>支付步骤
                    </h6>
                    <div class="step-item">
                        <div class="step-number">1</div>
                        <span>打开手机支付应用</span>
                    </div>
                    <div class="step-item">
                        <div class="step-number">2</div>
                        <span>扫描上方二维码</span>
                    </div>
                    <div class="step-item">
                        <div class="step-number">3</div>
                        <span>确认支付金额</span>
                    </div>
                    <div class="step-item mb-0">
                        <div class="step-number">4</div>
                        <span>完成支付等待跳转</span>
                    </div>
                </div>

                <!-- 返回按钮 -->
                <div class="mt-4">
                    <a href="{{ url('/') }}" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left me-2"></i>返回首页
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 订单状态检查
    const checkOrderStatus = {
        url: '{{ url('check-order-status', ['orderSN' => $orderid]) }}',
        type: 'GET',
        dataType: 'json',
        success: function(res) {
            if (res.code == 400001) {
                // 订单超时
                clearInterval(statusTimer);
                clearInterval(countdownTimer);

                document.querySelector('.status-indicator').innerHTML =
                    '<div class="text-danger"><i class="fas fa-times-circle me-2"></i>订单已过期</div>';

                showToast('订单支付超时', 'error');
                setTimeout(() => {
                    window.location.href = '/';
                }, 3000);
            }

            if (res.code == 200) {
                // 支付成功
                clearInterval(statusTimer);
                clearInterval(countdownTimer);

                document.querySelector('.status-indicator').innerHTML =
                    '<div class="text-success"><i class="fas fa-check-circle me-2"></i>支付成功</div>';

                showToast('支付成功！正在跳转...', 'success');
                setTimeout(() => {
                    window.location.href = '{{ url('detail-order-sn', ['orderSN' => $orderid]) }}';
                }, 2000);
            }
        },
        error: function() {
            console.log('检查订单状态失败');
        }
    };

    // 每5秒检查一次订单状态
    const statusTimer = setInterval(() => {
        $.ajax(checkOrderStatus);
    }, 5000);

    // 倒计时功能
    let timeLeft = {{ dujiaoka_config_get('order_expire_time', 5) }} * 60; // 转换为秒

    function updateCountdown() {
        if (timeLeft > 0) {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            document.getElementById('countdown').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            timeLeft--;
        } else {
            document.getElementById('countdown').textContent = '已过期';
            clearInterval(countdownTimer);
            clearInterval(statusTimer);

            document.querySelector('.status-indicator').innerHTML =
                '<div class="text-danger"><i class="fas fa-times-circle me-2"></i>订单已过期</div>';
        }
    }

    // 每秒更新倒计时
    updateCountdown();
    const countdownTimer = setInterval(updateCountdown, 1000);

    // 页面可见性检测，页面重新可见时立即检查状态
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            $.ajax(checkOrderStatus);
        }
    });

    // 二维码点击放大功能
    document.querySelector('.qr-code').addEventListener('click', function() {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            cursor: pointer;
        `;

        const img = document.createElement('img');
        img.src = this.src;
        img.style.cssText = `
            max-width: 90%;
            max-height: 90%;
            border-radius: 12px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
        `;

        modal.appendChild(img);
        document.body.appendChild(modal);

        modal.addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    });

    console.log('Enterprise QR Payment Page initialized successfully');
});
</script>
@endsection
