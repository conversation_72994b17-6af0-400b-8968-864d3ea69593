@extends('neon.layouts.default')

@section('header')
<style>
/* 企业级订单页面样式 */
.order-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    transition: all 0.3s ease;
}

.order-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.order-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    text-align: center;
    display: inline-block;
}

.status-completed { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; }
.status-pending { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; }
.status-processing { background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%); color: white; }
.status-wait-pay { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; }
.status-expired { background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%); color: white; }
.status-failure { background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); color: white; }
.status-abnormal { background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%); color: white; }

.order-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.detail-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.copy-button {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    padding: 0.25rem 0.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.copy-button:hover {
    background: var(--primary-gradient);
    transform: scale(1.1);
}

.breadcrumb-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem 1.5rem;
}

.card-content-area {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.08);
}
</style>
@endsection

@section('content')
<!-- 企业级订单信息页面 -->
<div class="container py-5">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb-glass mb-5" aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-shopping-bag me-1"></i>购物
                </a>
            </li>
            <li class="breadcrumb-item active text-white" aria-current="page">订单详情</li>
        </ol>
    </nav>

    @if(count($orders) > 0)
        <div class="row g-4">
            @foreach($orders as $index => $order)
            <div class="col-12">
                <div class="order-card p-4">
                    <!-- 订单头部信息 -->
                    <div class="d-flex justify-content-between align-items-start mb-4">
                        <div>
                            <h4 class="gradient-text mb-2">
                                <i class="fas fa-receipt me-2"></i>订单 #{{ $order['order_sn'] }}
                            </h4>
                            <p class="text-light mb-0">
                                <i class="fas fa-clock me-1"></i>
                                创建时间：{{ $order['created_at'] }}
                            </p>
                        </div>
                        <div class="text-end">
                            <div class="order-status
                                @switch($order['status'])
                                    @case(\App\Models\Order::STATUS_COMPLETED)
                                        status-completed
                                        @break
                                    @case(\App\Models\Order::STATUS_PENDING)
                                        status-pending
                                        @break
                                    @case(\App\Models\Order::STATUS_PROCESSING)
                                        status-processing
                                        @break
                                    @case(\App\Models\Order::STATUS_WAIT_PAY)
                                        status-wait-pay
                                        @break
                                    @case(\App\Models\Order::STATUS_EXPIRED)
                                        status-expired
                                        @break
                                    @case(\App\Models\Order::STATUS_FAILURE)
                                        status-failure
                                        @break
                                    @case(\App\Models\Order::STATUS_ABNORMAL)
                                        status-abnormal
                                        @break
                                    @default
                                        status-pending
                                @endswitch
                            ">
                                @switch($order['status'])
                                    @case(\App\Models\Order::STATUS_EXPIRED)
                                        <i class="fas fa-times-circle me-1"></i>已过期
                                        @break
                                    @case(\App\Models\Order::STATUS_WAIT_PAY)
                                        <i class="fas fa-clock me-1"></i>待支付
                                        @break
                                    @case(\App\Models\Order::STATUS_PENDING)
                                        <i class="fas fa-hourglass-half me-1"></i>待处理
                                        @break
                                    @case(\App\Models\Order::STATUS_PROCESSING)
                                        <i class="fas fa-cog me-1"></i>处理中
                                        @break
                                    @case(\App\Models\Order::STATUS_COMPLETED)
                                        <i class="fas fa-check-circle me-1"></i>已完成
                                        @break
                                    @case(\App\Models\Order::STATUS_FAILURE)
                                        <i class="fas fa-exclamation-circle me-1"></i>已失败
                                        @break
                                    @case(\App\Models\Order::STATUS_ABNORMAL)
                                        <i class="fas fa-exclamation-triangle me-1"></i>状态异常
                                        @break
                                    @default
                                        <i class="fas fa-question-circle me-1"></i>未知状态
                                @endswitch
                            </div>
                            @if($order['status'] == \App\Models\Order::STATUS_WAIT_PAY)
                            <div class="mt-2">
                                <a href="{{ url('/bill/'.$order['order_sn']) }}" class="btn btn-gradient btn-sm">
                                    <i class="fas fa-credit-card me-1"></i>立即支付
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>

                    <!-- 订单详情网格 -->
                    <div class="order-details-grid mb-4">
                        <!-- 商品信息 -->
                        <div class="detail-item">
                            <h6 class="text-white mb-3">
                                <i class="fas fa-box me-2"></i>商品信息
                            </h6>
                            <div class="text-light">
                                <p class="mb-2">
                                    <strong>商品名称：</strong>{{ $order['title'] }}
                                </p>
                                <p class="mb-2">
                                    <strong>购买数量：</strong>{{ $order['buy_amount'] }} 件
                                </p>
                                <p class="mb-0">
                                    <strong>发货方式：</strong>
                                    @if($order['type'] == \App\Models\Order::AUTOMATIC_DELIVERY)
                                        <span class="badge bg-success">
                                            <i class="fas fa-bolt me-1"></i>自动发货
                                        </span>
                                    @else
                                        <span class="badge bg-info">
                                            <i class="fas fa-user me-1"></i>人工发货
                                        </span>
                                    @endif
                                </p>
                            </div>
                        </div>

                        <!-- 订单信息 -->
                        <div class="detail-item">
                            <h6 class="text-white mb-3">
                                <i class="fas fa-info-circle me-2"></i>订单信息
                            </h6>
                            <div class="text-light">
                                <p class="mb-2">
                                    <strong>订单编号：</strong>
                                    <span id="orderSn-{{ $index }}">{{ $order['order_sn'] }}</span>
                                    <button class="copy-button ms-2" onclick="copyToClipboard('orderSn-{{ $index }}')" title="复制订单号">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </p>
                                <p class="mb-2">
                                    <strong>下单邮箱：</strong>{{ $order['email'] }}
                                </p>
                                <p class="mb-0">
                                    <strong>创建时间：</strong>{{ $order['created_at'] }}
                                </p>
                            </div>
                        </div>

                        <!-- 支付信息 -->
                        <div class="detail-item">
                            <h6 class="text-white mb-3">
                                <i class="fas fa-credit-card me-2"></i>支付信息
                            </h6>
                            <div class="text-light">
                                <p class="mb-2">
                                    <strong>订单金额：</strong>
                                    <span class="text-success fw-bold">¥{{ number_format($order['actual_price'], 2) }}</span>
                                </p>
                                <p class="mb-0">
                                    <strong>支付方式：</strong>{{ $order['pay_way'] ?? '未选择' }}
                                </p>
                            </div>
                        </div>

                        <!-- 其他信息 -->
                        @if(!empty($order['info']))
                        <div class="detail-item">
                            <h6 class="text-white mb-3">
                                <i class="fas fa-list me-2"></i>其他信息
                            </h6>
                            <div class="text-light">
                                @foreach($order['info'] as $key => $value)
                                <p class="mb-2">
                                    <strong>{{ $key }}：</strong>{{ $value }}
                                </p>
                                @endforeach
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- 商品内容区域 -->
                    @if(!empty($order['info']) && isset($order['info']['card_content']))
                    <div class="card-content-area">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-gift me-2"></i>商品内容
                        </h6>
                        <div class="text-light">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>商品卡密信息</span>
                                <button class="copy-button" onclick="copyToClipboard('cardContent-{{ $index }}')" title="复制全部内容">
                                    <i class="fas fa-copy me-1"></i>复制全部
                                </button>
                            </div>
                            <div class="bg-dark p-3 rounded" style="font-family: monospace;">
                                <pre id="cardContent-{{ $index }}" class="text-light mb-0">{{ $order['info']['card_content'] }}</pre>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endforeach
        </div>
    @else
        <!-- 无订单状态 -->
        <div class="text-center py-5">
            <div class="order-card p-5">
                <i class="fas fa-search fa-4x text-muted mb-4"></i>
                <h4 class="text-white mb-3">未找到订单信息</h4>
                <p class="text-light mb-4">请检查您的订单号和查询密码是否正确</p>
                <a href="{{ url('/') }}" class="btn btn-gradient">
                    <i class="fas fa-home me-2"></i>返回首页
                </a>
            </div>
        </div>
    @endif
</div>
@endsection

@section('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 复制到剪贴板功能
    window.copyToClipboard = function(elementId) {
        const element = document.getElementById(elementId);
        if (!element) {
            showToast('复制失败：找不到内容', 'error');
            return;
        }

        const text = element.textContent || element.innerText;

        if (navigator.clipboard && window.isSecureContext) {
            // 使用现代 Clipboard API
            navigator.clipboard.writeText(text).then(() => {
                showToast('复制成功！', 'success');
            }).catch(() => {
                fallbackCopyTextToClipboard(text);
            });
        } else {
            // 降级方案
            fallbackCopyTextToClipboard(text);
        }
    };

    // 降级复制方案
    function fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showToast('复制成功！', 'success');
            } else {
                showToast('复制失败，请手动复制', 'error');
            }
        } catch (err) {
            showToast('复制失败，请手动复制', 'error');
        }

        document.body.removeChild(textArea);
    }

    // 订单状态自动刷新（仅对待支付和处理中的订单）
    const pendingOrders = document.querySelectorAll('.status-wait-pay, .status-pending, .status-processing');
    if (pendingOrders.length > 0) {
        // 每30秒刷新一次页面检查订单状态
        setTimeout(() => {
            window.location.reload();
        }, 30000);
    }

    console.log('Enterprise Order Info Page initialized successfully');
});
</script>
@endsection