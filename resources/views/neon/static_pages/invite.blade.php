@extends('neon.layouts.default')

@section('header')
<style>
/* 企业级邀请页面样式 */
.invite-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 40vh;
    position: relative;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.invite-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.stats-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.stats-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
}

.stats-value {
    font-size: 2rem;
    font-weight: bold;
    color: white;
    margin-bottom: 0.5rem;
}

.stats-label {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
}

.invite-tabs {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 0.5rem;
    margin-bottom: 2rem;
}

.invite-tab {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
}

.invite-tab.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.invite-tab:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.invite-link-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
}

.invite-link-input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    padding: 1rem 1.5rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.invite-link-input:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    color: white;
}

.breadcrumb-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem 1.5rem;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: floatUpDown 8s ease-in-out infinite;
}

@keyframes floatUpDown {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

.order-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.order-item:last-child {
    margin-bottom: 0;
}

.announcement-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 2rem;
}
</style>
@endsection

@section('content')
<!-- 企业级邀请页面 -->

<!-- 邀请页面头部 -->
<section class="invite-hero d-flex align-items-center">
    <!-- 浮动装饰元素 -->
    <div class="floating-elements">
        <div class="floating-element" style="left: 10%; top: 20%; font-size: 2rem;">
            <i class="fas fa-users"></i>
        </div>
        <div class="floating-element" style="left: 80%; top: 30%; font-size: 1.5rem;">
            <i class="fas fa-gift"></i>
        </div>
        <div class="floating-element" style="left: 15%; top: 70%; font-size: 1.8rem;">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="floating-element" style="left: 75%; top: 80%; font-size: 1.3rem;">
            <i class="fas fa-money-bill-wave"></i>
        </div>
    </div>

    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold text-white mb-4">
                    <i class="fas fa-user-friends me-3"></i>邀请返利
                </h1>
                <p class="lead text-white mb-0">
                    邀请好友注册购买，获得丰厚返利奖励
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex justify-content-lg-end gap-3">
                    <div class="text-center text-white">
                        <h3 class="mb-1">{{ $invite_count }}</h3>
                        <small>邀请订单</small>
                    </div>
                    <div class="text-center text-white">
                        <h3 class="mb-1">¥{{ number_format($invite_amount, 2) }}</h3>
                        <small>可提现金额</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb-glass mb-5" aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="/user" class="text-light text-decoration-none">
                    <i class="fas fa-user me-1"></i>用户中心
                </a>
            </li>
            <li class="breadcrumb-item active text-white" aria-current="page">邀请返利</li>
        </ol>
    </nav>

    <div class="row g-4">
        <!-- 左侧主要内容 -->
        <div class="col-lg-9">
            <!-- 统计卡片 -->
            <div class="row g-4 mb-5">
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stats-value">{{ $invite_count }}</div>
                        <div class="stats-label">返利订单数</div>
                        <button type="button" class="btn btn-gradient btn-sm" data-bs-toggle="modal" data-bs-target="#withdrawalModal">
                            <i class="fas fa-money-bill-wave me-1"></i>提现设置
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="stats-value">¥{{ number_format($invite_amount, 2) }}</div>
                        <div class="stats-label">可提现金额</div>
                        <button type="button" class="btn btn-gradient btn-sm" onclick="showToast('提现功能开发中', 'info')">
                            <i class="fas fa-hand-holding-usd me-1"></i>申请提现
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stats-value">{{ dujiaoka_config_get('invite_rate', 10) }}%</div>
                        <div class="stats-label">返利比例</div>
                        <small class="text-light">每笔订单返利比例</small>
                    </div>
                </div>
            </div>
            <!-- 邀请记录 -->
            <div class="invite-card">
                <!-- 标签页导航 -->
                <div class="invite-tabs">
                    <div class="d-flex">
                        <a href="{{ url('/user/invite') }}" class="invite-tab {{ $type === 'invite' ? 'active' : '' }}">
                            <i class="fas fa-chart-line me-2"></i>返利记录
                        </a>
                        <a href="{{ url('/user/invite/withdraw') }}" class="invite-tab {{ $type === 'withdraw' ? 'active' : '' }}">
                            <i class="fas fa-money-bill-wave me-2"></i>提现记录
                        </a>
                    </div>
                </div>

                <!-- 记录列表 -->
                @if($type === 'invite')
                <!-- 返利记录 -->
                <div class="table-responsive">
                    @if(count($orders) > 0)
                    @foreach($orders as $order)
                    <div class="order-item">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <small class="text-light">ID: {{ $order->id }}</small>
                            </div>
                            <div class="col-md-3">
                                <h6 class="text-white mb-1">返利用户</h6>
                                <small class="text-light">{{ $order->order ? \App\Service\Util::CryptoMail($order->order->email) : '无邮箱信息' }}</small>
                            </div>
                            <div class="col-md-2">
                                <h6 class="text-white mb-1">返利金额</h6>
                                <div class="text-success fw-bold">¥{{ number_format($order->amount, 2) }}</div>
                            </div>
                            <div class="col-md-2">
                                <h6 class="text-white mb-1">状态</h6>
                                @if($order->status === 1)
                                    <span class="badge bg-success">已提现</span>
                                @else
                                    <span class="badge bg-primary">可提现</span>
                                @endif
                            </div>
                            <div class="col-md-3">
                                <h6 class="text-white mb-1">创建时间</h6>
                                <small class="text-light">{{ $order->created_at->format('Y-m-d H:i') }}</small>
                            </div>
                        </div>
                    </div>
                    @endforeach
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-chart-line fa-4x text-muted mb-3"></i>
                        <h6 class="text-white mb-2">暂无返利记录</h6>
                        <p class="text-light mb-4">邀请好友购买后将显示返利记录</p>
                    </div>
                    @endif
                </div>
                @else
                <!-- 提现记录 -->
                <div class="table-responsive">
                    @if(count($orders) > 0)
                    @foreach($orders as $order)
                    <div class="order-item">
                        <div class="row align-items-center">
                            <div class="col-md-2">
                                <small class="text-light">ID: {{ $order->id }}</small>
                            </div>
                            <div class="col-md-2">
                                <h6 class="text-white mb-1">提现金额</h6>
                                <div class="text-success fw-bold">¥{{ number_format($order->amount, 2) }}</div>
                            </div>
                            <div class="col-md-2">
                                <h6 class="text-white mb-1">提现方式</h6>
                                @if($order->type === 2)
                                    <small class="text-light">{{ \App\Service\Util::WithdrawAccount($order->account) }}</small>
                                @else
                                    <small class="text-light">转余额</small>
                                @endif
                            </div>
                            <div class="col-md-2">
                                <h6 class="text-white mb-1">提现地址</h6>
                                <small class="text-light">{{ Str::limit($order->address, 20) }}</small>
                            </div>
                            <div class="col-md-2">
                                <h6 class="text-white mb-1">状态</h6>
                                @if($order->status === 1)
                                    <span class="badge bg-success">已完成</span>
                                @else
                                    <span class="badge bg-warning">审核中</span>
                                @endif
                            </div>
                            <div class="col-md-2">
                                <h6 class="text-white mb-1">创建时间</h6>
                                <small class="text-light">{{ $order->created_at->format('Y-m-d H:i') }}</small>
                            </div>
                        </div>
                    </div>
                    @endforeach
                    @else
                    <div class="text-center py-5">
                        <i class="fas fa-money-bill-wave fa-4x text-muted mb-3"></i>
                        <h6 class="text-white mb-2">暂无提现记录</h6>
                        <p class="text-light mb-4">申请提现后将显示提现记录</p>
                    </div>
                    @endif
                </div>
                @endif

                <!-- 分页 -->
                @if($orders->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $orders->links() }}
                </div>
                @endif
            </div>
        </div>
        <!-- 右侧侧边栏 -->
        <div class="col-lg-3">
            <!-- 邀请链接 -->
            <div class="invite-link-card mb-4">
                <h6 class="text-white mb-3">
                    <i class="fas fa-link me-2"></i>邀请链接
                </h6>
                <div class="mb-3">
                    <label class="form-label text-white mb-2">专属邀请链接</label>
                    <div class="input-group">
                        <input type="text" class="invite-link-input"
                               value="{{ config('app.url') }}?aff={{ Auth::user()->invite_code }}"
                               readonly id="inviteLink">
                        <button class="btn btn-gradient" type="button" onclick="copyInviteLink()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label text-white mb-2">邀请码</label>
                    <div class="input-group">
                        <input type="text" class="invite-link-input"
                               value="{{ Auth::user()->invite_code }}"
                               readonly id="inviteCode">
                        <button class="btn btn-gradient" type="button" onclick="copyInviteCode()">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 活动规则 -->
            <div class="announcement-card">
                <h6 class="text-white mb-3">
                    <i class="fas fa-bullhorn me-2"></i>活动规则
                </h6>
                <div class="text-light">
                    {!! dujiaoka_config_get('guize_text', '
                    <p>1. 邀请好友注册并购买商品</p>
                    <p>2. 获得订单金额的返利奖励</p>
                    <p>3. 返利金额可提现或转余额</p>
                    <p>4. 提现需要满足最低金额要求</p>
                    ') !!}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 企业级提现模态框 -->
<div class="modal fade" id="withdrawalModal" tabindex="-1" aria-labelledby="withdrawalModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
            <div class="modal-header border-0">
                <h5 class="modal-title text-white" id="withdrawalModalLabel">
                    <i class="fas fa-money-bill-wave me-2"></i>返利提现
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>

            <div class="modal-body">
                <!-- 可提现金额 -->
                <div class="text-center mb-4">
                    <h6 class="text-white mb-2">可提现金额</h6>
                    <div class="h3 text-success mb-0">¥{{ number_format($invite_amount, 2) }}</div>
                </div>

                <!-- 提现类型选择 -->
                <div class="mb-4">
                    <h6 class="text-white mb-3">
                        <i class="fas fa-exchange-alt me-2"></i>提现类型
                    </h6>
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="payment-method-card selected" data-type="balance" onclick="changeWithdrawType('balance')">
                                <div class="text-center">
                                    <i class="fas fa-wallet fa-2x text-primary mb-2"></i>
                                    <h6 class="text-white mb-1">转余额</h6>
                                    <small class="text-light">转入账户余额</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="payment-method-card" data-type="withdraw" onclick="changeWithdrawType('withdraw')">
                                <div class="text-center">
                                    <i class="fas fa-credit-card fa-2x text-success mb-2"></i>
                                    <h6 class="text-white mb-1">提现账户</h6>
                                    <small class="text-light">提现到外部账户</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提现账户类型（仅在选择提现账户时显示） -->
                <div id="withdrawAccountSection" style="display: none;">
                    <div class="mb-4">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-university me-2"></i>账户类型
                        </h6>
                        <div class="row g-2">
                            <div class="col-4">
                                <div class="payment-method-card selected" data-account="USDT" onclick="changeAccount('USDT')">
                                    <div class="text-center">
                                        <i class="fab fa-bitcoin fa-2x text-warning mb-2"></i>
                                        <small class="text-white">USDT</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="payment-method-card" data-account="alipay" onclick="changeAccount('alipay')">
                                    <div class="text-center">
                                        <i class="fab fa-alipay fa-2x text-primary mb-2"></i>
                                        <small class="text-white">支付宝</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="payment-method-card" data-account="wechat" onclick="changeAccount('wechat')">
                                    <div class="text-center">
                                        <i class="fab fa-weixin fa-2x text-success mb-2"></i>
                                        <small class="text-white">微信</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 账户地址 -->
                    <div class="mb-4">
                        <label class="form-label text-white mb-2">
                            <i class="fas fa-map-marker-alt me-2"></i>账户地址
                        </label>
                        <input type="text" name="address" class="form-control-glass"
                               placeholder="请输入提现账户地址" id="withdrawAddress">
                    </div>
                </div>

                <!-- 提现说明 -->
                <div class="mb-4">
                    <h6 class="text-white mb-3">
                        <i class="fas fa-info-circle me-2"></i>提现说明
                    </h6>
                    <div class="text-light small">
                        {!! dujiaoka_config_get('tixian_text', '
                        <p>1. 最低提现金额：10元</p>
                        <p>2. 提现申请将在1-3个工作日内处理</p>
                        <p>3. 请确保提现地址正确，错误地址导致的损失自负</p>
                        <p>4. 转余额操作即时到账</p>
                        ') !!}
                    </div>
                </div>
            </div>

            <div class="modal-footer border-0">
                <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>取消
                </button>
                <button type="button" class="btn btn-gradient" onclick="submitWithdraw()">
                    <i class="fas fa-paper-plane me-1"></i>提交申请
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 提现参数
    let withdrawParam = {
        type: 'balance',
        address: '',
        account: 'USDT',
        _token: "{{ csrf_token() }}",
    };

    // 复制邀请链接
    window.copyInviteLink = function() {
        const link = document.getElementById('inviteLink').value;
        copyToClipboard(link, '邀请链接已复制');
    };

    // 复制邀请码
    window.copyInviteCode = function() {
        const code = document.getElementById('inviteCode').value;
        copyToClipboard(code, '邀请码已复制');
    };

    // 复制到剪贴板通用函数
    function copyToClipboard(text, message) {
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                showToast(message, 'success');
            });
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showToast(message, 'success');
            } catch (err) {
                showToast('复制失败，请手动复制', 'error');
            }

            document.body.removeChild(textArea);
        }
    }

    // 改变提现类型
    window.changeWithdrawType = function(type) {
        withdrawParam.type = type;

        // 更新UI
        document.querySelectorAll('[data-type]').forEach(el => el.classList.remove('selected'));
        document.querySelector(`[data-type="${type}"]`).classList.add('selected');

        // 显示/隐藏账户选择
        const accountSection = document.getElementById('withdrawAccountSection');
        if (type === 'withdraw') {
            accountSection.style.display = 'block';
        } else {
            accountSection.style.display = 'none';
        }
    };

    // 改变账户类型
    window.changeAccount = function(account) {
        withdrawParam.account = account;

        // 更新UI
        document.querySelectorAll('[data-account]').forEach(el => el.classList.remove('selected'));
        document.querySelector(`[data-account="${account}"]`).classList.add('selected');
    };

    // 提交提现申请
    window.submitWithdraw = function() {
        withdrawParam.address = document.getElementById('withdrawAddress').value;

        if (withdrawParam.type === 'withdraw' && !withdrawParam.address) {
            showToast('请输入提现账户地址', 'error');
            return;
        }

        // 显示加载状态
        const submitBtn = document.querySelector('[onclick="submitWithdraw()"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>提交中...';

        // 发送请求
        fetch('/user/withdraw', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': withdrawParam._token
            },
            body: JSON.stringify(withdrawParam)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code) {
                showToast(data.message, 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('提交失败，请重试', 'error');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    };

    console.log('Enterprise Invite Page initialized successfully');
});
</script>
@endsection
