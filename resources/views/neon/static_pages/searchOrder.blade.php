@extends('neon.layouts.default')

@section('header')
<style>
/* 企业级订单查询页面样式 */
.search-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 60vh;
    position: relative;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.search-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.search-tabs {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 0.5rem;
    margin-bottom: 2rem;
}

.search-tab {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 1rem 1.5rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    flex: 1;
    text-align: center;
}

.search-tab.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.search-tab:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.form-control-glass {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    padding: 1rem 1.5rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.form-control-glass:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    color: white;
}

.form-control-glass::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.breadcrumb-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem 1.5rem;
}

.info-alert {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    border-radius: 15px;
    color: white;
    padding: 1rem 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
}

.search-method-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 1rem;
}

.search-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: floatUpDown 8s ease-in-out infinite;
}

@keyframes floatUpDown {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}
</style>
@endsection

@section('content')
<!-- 企业级订单查询页面 -->
<section class="search-hero d-flex align-items-center">
    <!-- 浮动装饰元素 -->
    <div class="floating-elements">
        <div class="floating-element" style="left: 10%; top: 20%; font-size: 2rem;">
            <i class="fas fa-search"></i>
        </div>
        <div class="floating-element" style="left: 80%; top: 30%; font-size: 1.5rem;">
            <i class="fas fa-receipt"></i>
        </div>
        <div class="floating-element" style="left: 15%; top: 70%; font-size: 1.8rem;">
            <i class="fas fa-list-alt"></i>
        </div>
        <div class="floating-element" style="left: 75%; top: 80%; font-size: 1.3rem;">
            <i class="fas fa-history"></i>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center text-white mb-5">
                    <h1 class="display-4 fw-bold mb-4">
                        <i class="fas fa-search me-3"></i>订单查询
                    </h1>
                    <p class="lead">
                        快速查询您的订单状态和详细信息
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb-glass mb-5" aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-shopping-bag me-1"></i>购物
                </a>
            </li>
            <li class="breadcrumb-item active text-white" aria-current="page">订单查询</li>
        </ol>
    </nav>

    <!-- 提示信息 -->
    <div class="row justify-content-center mb-5">
        <div class="col-lg-8">
            <div class="info-alert text-center">
                <i class="fas fa-info-circle fa-2x mb-3"></i>
                <h6 class="mb-2">查询说明</h6>
                <p class="mb-0">为保护您的隐私，最多只能查询近10笔订单记录</p>
            </div>
        </div>
    </div>

    <!-- 查询表单 -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="search-card">
                <!-- 查询方式选择 -->
                <div class="search-tabs">
                    <div class="d-flex">
                        <button class="search-tab active" data-tab="order_search_by_sn">
                            <i class="fas fa-hashtag me-2"></i>订单号查询
                        </button>
                        <button class="search-tab" data-tab="order_search_by_email">
                            <i class="fas fa-envelope me-2"></i>邮箱查询
                        </button>
                        <button class="search-tab" data-tab="order_search_by_browser">
                            <i class="fas fa-globe me-2"></i>浏览器查询
                        </button>
                    </div>
                </div>
                <!-- 查询表单内容 -->
                <div class="tab-content">
                    <!-- 订单号查询 -->
                    <div class="tab-pane active" id="order_search_by_sn">
                        <div class="search-method-card">
                            <div class="search-icon">
                                <i class="fas fa-hashtag"></i>
                            </div>
                            <h5 class="text-white text-center mb-3">订单号查询</h5>
                            <p class="text-light text-center mb-4">
                                输入完整的订单编号进行精确查询
                            </p>

                            <form class="needs-validation" action="{{ url('search-order-by-sn') }}" method="post" novalidate>
                                @csrf
                                <div class="mb-4">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-receipt me-2"></i>订单编号
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" name="order_sn" class="form-control-glass"
                                           placeholder="请输入完整的订单编号" required>
                                    <div class="invalid-feedback">请输入订单编号!</div>
                                    <small class="text-light mt-1 d-block">
                                        <i class="fas fa-info-circle me-1"></i>
                                        订单编号通常以字母开头，包含数字和字母
                                    </small>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <button type="submit" class="btn btn-gradient px-4 py-2">
                                        <i class="fas fa-search me-2"></i>立即查询
                                    </button>
                                    <button type="reset" class="btn btn-outline-light px-4 py-2">
                                        <i class="fas fa-redo me-2"></i>重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 邮箱查询 -->
                    <div class="tab-pane" id="order_search_by_email">
                        <div class="search-method-card">
                            <div class="search-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <h5 class="text-white text-center mb-3">邮箱查询</h5>
                            <p class="text-light text-center mb-4">
                                使用下单时的邮箱地址查询所有相关订单
                            </p>

                            <form class="needs-validation" action="{{ url('search-order-by-email') }}" method="post" novalidate>
                                @csrf
                                <div class="mb-4">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-envelope me-2"></i>邮箱地址
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="email" name="email" class="form-control-glass"
                                           placeholder="请输入下单时使用的邮箱" required>
                                    <div class="invalid-feedback">请输入有效的邮箱地址!</div>
                                </div>

                                @if(dujiaoka_config_get('is_open_search_pwd', \App\Models\BaseModel::STATUS_CLOSE) == \App\Models\BaseModel::STATUS_OPEN)
                                <div class="mb-4">
                                    <label class="form-label text-white mb-2">
                                        <i class="fas fa-key me-2"></i>查询密码
                                        <span class="text-danger">*</span>
                                    </label>
                                    <input type="password" name="search_pwd" class="form-control-glass"
                                           placeholder="请输入下单时设置的查询密码" required>
                                    <div class="invalid-feedback">请输入查询密码!</div>
                                    <small class="text-light mt-1 d-block">
                                        <i class="fas fa-shield-alt me-1"></i>
                                        查询密码是您下单时设置的安全密码
                                    </small>
                                </div>
                                @endif

                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <button type="submit" class="btn btn-gradient px-4 py-2">
                                        <i class="fas fa-search me-2"></i>立即查询
                                    </button>
                                    <button type="reset" class="btn btn-outline-light px-4 py-2">
                                        <i class="fas fa-redo me-2"></i>重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 浏览器查询 -->
                    <div class="tab-pane" id="order_search_by_browser">
                        <div class="search-method-card">
                            <div class="search-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <h5 class="text-white text-center mb-3">浏览器查询</h5>
                            <p class="text-light text-center mb-4">
                                查询当前浏览器中保存的订单记录
                            </p>

                            <div class="alert alert-info border-0 rounded-3 mb-4" style="background: rgba(79, 172, 254, 0.1); color: rgba(255, 255, 255, 0.9);">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle fa-2x me-3"></i>
                                    <div>
                                        <h6 class="mb-1">温馨提示</h6>
                                        <p class="mb-0 small">此功能会查询浏览器本地存储的订单信息，仅显示在当前设备和浏览器中创建的订单</p>
                                    </div>
                                </div>
                            </div>

                            <form class="needs-validation" action="{{ url('search-order-by-browser') }}" method="post">
                                @csrf
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-gradient px-4 py-3">
                                        <i class="fas fa-history me-2"></i>查询浏览器订单记录
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 查询帮助 -->
    <div class="row justify-content-center mt-5">
        <div class="col-lg-8">
            <div class="search-card">
                <h5 class="text-white text-center mb-4">
                    <i class="fas fa-question-circle me-2"></i>查询帮助
                </h5>
                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="search-icon mx-auto mb-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                <i class="fas fa-hashtag"></i>
                            </div>
                            <h6 class="text-white mb-2">订单号查询</h6>
                            <p class="text-light small">
                                最精确的查询方式，输入完整订单号即可查询
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="search-icon mx-auto mb-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <h6 class="text-white mb-2">邮箱查询</h6>
                            <p class="text-light small">
                                查询该邮箱下的所有订单，需要查询密码验证
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="search-icon mx-auto mb-3" style="width: 50px; height: 50px; font-size: 1.2rem;">
                                <i class="fas fa-globe"></i>
                            </div>
                            <h6 class="text-white mb-2">浏览器查询</h6>
                            <p class="text-light small">
                                查询当前浏览器中的订单缓存记录
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 标签页切换功能
    const tabs = document.querySelectorAll('.search-tab');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // 移除所有活动状态
            tabs.forEach(t => t.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // 添加活动状态
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });

    // 表单验证和提交处理
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!form.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();
            } else {
                // 显示加载状态
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>查询中...';

                // 如果验证失败，恢复按钮状态
                setTimeout(() => {
                    if (!form.checkValidity()) {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    }
                }, 100);
            }

            form.classList.add('was-validated');
        });
    });

    // 重置按钮功能
    const resetBtns = document.querySelectorAll('button[type="reset"]');
    resetBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const form = this.closest('form');
            form.classList.remove('was-validated');

            // 重置所有输入框的样式
            const inputs = form.querySelectorAll('.form-control-glass');
            inputs.forEach(input => {
                input.classList.remove('is-valid', 'is-invalid');
            });
        });
    });

    // 输入框焦点效果
    document.querySelectorAll('.form-control-glass').forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });

    // 订单号格式提示
    const orderSnInput = document.querySelector('input[name="order_sn"]');
    if (orderSnInput) {
        orderSnInput.addEventListener('input', function() {
            const value = this.value.toUpperCase();
            this.value = value;

            // 简单的格式验证提示
            if (value.length > 0 && !/^[A-Z0-9]/.test(value)) {
                this.setCustomValidity('订单号格式不正确');
            } else {
                this.setCustomValidity('');
            }
        });
    }

    console.log('Enterprise Order Search Page initialized successfully');
});
</script>
@endsection
