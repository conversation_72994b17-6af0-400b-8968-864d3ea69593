@extends('neon.layouts.default')

@section('header')
<style>
/* 企业级用户中心样式 */
.user-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 40vh;
    position: relative;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.user-sidebar {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    position: sticky;
    top: 2rem;
}

.user-avatar {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto 1rem;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.user-nav-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.user-nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.user-nav-item.active {
    background: var(--primary-gradient);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.user-nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    border-radius: 12px;
}

.user-nav-item.active .user-nav-link {
    color: white;
}

.stats-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.stats-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
}

.stats-value {
    font-size: 2rem;
    font-weight: bold;
    color: white;
    margin-bottom: 0.5rem;
}

.stats-label {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1rem;
}

.main-content {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 2rem;
}

.breadcrumb-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 1rem 1.5rem;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.floating-element {
    position: absolute;
    opacity: 0.1;
    animation: floatUpDown 8s ease-in-out infinite;
}

@keyframes floatUpDown {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

.quick-action-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.quick-action-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
}

.recent-orders {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.08);
}

.order-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.order-item:last-child {
    margin-bottom: 0;
}
</style>
@endsection

@section('content')
<!-- 企业级用户中心页面 -->

<!-- 用户中心头部 -->
<section class="user-hero d-flex align-items-center">
    <!-- 浮动装饰元素 -->
    <div class="floating-elements">
        <div class="floating-element" style="left: 10%; top: 20%; font-size: 2rem;">
            <i class="fas fa-user"></i>
        </div>
        <div class="floating-element" style="left: 80%; top: 30%; font-size: 1.5rem;">
            <i class="fas fa-chart-line"></i>
        </div>
        <div class="floating-element" style="left: 15%; top: 70%; font-size: 1.8rem;">
            <i class="fas fa-cog"></i>
        </div>
        <div class="floating-element" style="left: 75%; top: 80%; font-size: 1.3rem;">
            <i class="fas fa-star"></i>
        </div>
    </div>

    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold text-white mb-4">
                    <i class="fas fa-user-circle me-3"></i>用户中心
                </h1>
                <p class="lead text-white mb-0">
                    欢迎回来，{{ Auth::user()->email }}
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex justify-content-lg-end gap-3">
                    <div class="text-center text-white">
                        <h3 class="mb-1">${{ Auth::user()->money }}</h3>
                        <small>账户余额</small>
                    </div>
                    <div class="text-center text-white">
                        <h3 class="mb-1">{{ Auth::user()->orders_count ?? 0 }}</h3>
                        <small>订单数量</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- 面包屑导航 -->
    <nav class="breadcrumb-glass mb-5" aria-label="breadcrumb">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item">
                <a href="{{ url('/') }}" class="text-light text-decoration-none">
                    <i class="fas fa-home me-1"></i>首页
                </a>
            </li>
            <li class="breadcrumb-item active text-white" aria-current="page">用户中心</li>
        </ol>
    </nav>

    <div class="row g-4">
        <!-- 侧边栏导航 -->
        <div class="col-lg-3">
            <div class="user-sidebar">
                <!-- 用户头像和信息 -->
                <div class="text-center mb-4">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h5 class="text-white mb-1">{{ Auth::user()->email }}</h5>
                    <p class="text-light mb-0">
                        <i class="fas fa-crown me-1"></i>VIP会员
                    </p>
                </div>

                <!-- 导航菜单 -->
                <div class="mb-4">
                    <h6 class="text-white mb-3">
                        <i class="fas fa-tachometer-alt me-2"></i>控制面板
                    </h6>
                    <div class="user-nav-item active">
                        <a href="/user" class="user-nav-link">
                            <i class="fas fa-home me-3"></i>
                            <span>控制面板</span>
                        </a>
                    </div>
                    <div class="user-nav-item">
                        <a href="/user/invite" class="user-nav-link">
                            <i class="fas fa-gift me-3"></i>
                            <span>返利信息</span>
                        </a>
                    </div>
                    <div class="user-nav-item">
                        <a href="javascript:void(0);" class="user-nav-link" onclick="showToast('此功能暂未开通', 'info')">
                            <i class="fas fa-credit-card me-3"></i>
                            <span>付款方式</span>
                        </a>
                    </div>
                    <div class="user-nav-item">
                        <a href="javascript:void(0);" class="user-nav-link" onclick="showToast('此功能暂未开通', 'info')">
                            <i class="fas fa-star me-3"></i>
                            <span>我的评论</span>
                        </a>
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="text-white mb-3">
                        <i class="fas fa-user-cog me-2"></i>账户管理
                    </h6>
                    <div class="user-nav-item">
                        <a href="/user/account" class="user-nav-link">
                            <i class="fas fa-key me-3"></i>
                            <span>修改密码</span>
                        </a>
                    </div>
                    <div class="user-nav-item">
                        <a href="javascript:void(0);" class="user-nav-link" onclick="showToast('此功能暂未开通', 'info')">
                            <i class="fas fa-map-marker-alt me-3"></i>
                            <span>收货地址</span>
                        </a>
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="text-white mb-3">
                        <i class="fas fa-headset me-2"></i>客户服务
                    </h6>
                    <div class="user-nav-item">
                        <a href="javascript:void(0);" class="user-nav-link" onclick="showToast('此功能暂未开通', 'info')">
                            <i class="fas fa-question-circle me-3"></i>
                            <span>帮助中心</span>
                        </a>
                    </div>
                    <div class="user-nav-item">
                        <a href="javascript:void(0);" class="user-nav-link" onclick="showToast('此功能暂未开通', 'info')">
                            <i class="fas fa-file-contract me-3"></i>
                            <span>条款隐私</span>
                        </a>
                    </div>
                </div>

                <!-- 退出登录 -->
                <div class="user-nav-item">
                    <a href="/logout" class="user-nav-link text-danger">
                        <i class="fas fa-sign-out-alt me-3"></i>
                        <span>退出登录</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="col-lg-9">
            <!-- 统计卡片 -->
            <div class="row g-4 mb-5">
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="stats-value">${{ Auth::user()->money }}</div>
                        <div class="stats-label">账户余额</div>
                        <button type="button" class="btn btn-gradient btn-sm" data-bs-toggle="modal" data-bs-target="#rechargeModal">
                            <i class="fas fa-plus me-1"></i>充值余额
                        </button>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="fas fa-user-tag"></i>
                        </div>
                        <div class="stats-value">VIP</div>
                        <div class="stats-label">会员等级</div>
                        <a class="btn btn-gradient btn-sm" href="/user/account">
                            <i class="fas fa-edit me-1"></i>修改资料
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <div class="stats-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stats-value">{{ $invite_count }}</div>
                        <div class="stats-label">返利订单</div>
                        <a class="btn btn-gradient btn-sm" href="/user/invite">
                            <i class="fas fa-eye me-1"></i>查看详情
                        </a>
                    </div>
                </div>
            </div>

            <!-- 快捷操作 -->
            <div class="main-content mb-4">
                <h5 class="text-white mb-4">
                    <i class="fas fa-bolt me-2"></i>快捷操作
                </h5>
                <div class="row g-3">
                    <div class="col-md-3 col-6">
                        <div class="quick-action-card" data-bs-toggle="modal" data-bs-target="#rechargeModal">
                            <i class="fas fa-plus-circle fa-2x text-primary mb-2"></i>
                            <h6 class="text-white mb-1">充值余额</h6>
                            <small class="text-light">快速充值账户</small>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="quick-action-card" onclick="window.location.href='/user/invite'">
                            <i class="fas fa-gift fa-2x text-success mb-2"></i>
                            <h6 class="text-white mb-1">邀请返利</h6>
                            <small class="text-light">查看返利信息</small>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="quick-action-card" onclick="window.location.href='/search-order'">
                            <i class="fas fa-search fa-2x text-info mb-2"></i>
                            <h6 class="text-white mb-1">订单查询</h6>
                            <small class="text-light">查询订单状态</small>
                        </div>
                    </div>
                    <div class="col-md-3 col-6">
                        <div class="quick-action-card" onclick="window.location.href='/user/account'">
                            <i class="fas fa-cog fa-2x text-warning mb-2"></i>
                            <h6 class="text-white mb-1">账户设置</h6>
                            <small class="text-light">修改账户信息</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近订单 -->
            <div class="main-content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="text-white mb-0">
                        <i class="fas fa-shopping-bag me-2"></i>最近订单
                    </h5>
                    <a href="/search-order" class="btn btn-outline-light btn-sm">
                        <i class="fas fa-search me-1"></i>查看全部
                    </a>
                </div>

                @if(count($orders) > 0)
                <div class="recent-orders">
                    @foreach($orders->take(5) as $order)
                    <div class="order-item">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="text-white mb-1">{{ $order->title }}</h6>
                                <small class="text-light">
                                    订单号：{{ $order->order_sn }}
                                </small>
                            </div>
                            <div class="col-md-3 text-md-center">
                                @switch($order->status)
                                    @case(\App\Models\Order::STATUS_COMPLETED)
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>已完成
                                        </span>
                                        @break
                                    @case(\App\Models\Order::STATUS_PENDING)
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>待处理
                                        </span>
                                        @break
                                    @case(\App\Models\Order::STATUS_PROCESSING)
                                        <span class="badge bg-info">
                                            <i class="fas fa-cog me-1"></i>处理中
                                        </span>
                                        @break
                                    @case(\App\Models\Order::STATUS_WAIT_PAY)
                                        <span class="badge bg-danger">
                                            <i class="fas fa-credit-card me-1"></i>待支付
                                        </span>
                                        @break
                                    @default
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-question me-1"></i>未知
                                        </span>
                                @endswitch
                            </div>
                            <div class="col-md-3 text-md-end">
                                <div class="text-success fw-bold">¥{{ number_format($order->actual_price, 2) }}</div>
                                <small class="text-light">{{ $order->created_at->format('m-d H:i') }}</small>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                @else
                <div class="text-center py-5">
                    <i class="fas fa-shopping-bag fa-4x text-muted mb-3"></i>
                    <h6 class="text-white mb-2">暂无订单</h6>
                    <p class="text-light mb-4">您还没有任何订单记录</p>
                    <a href="/" class="btn btn-gradient">
                        <i class="fas fa-shopping-cart me-2"></i>立即购买
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- 企业级充值模态框 -->
<div class="modal fade" id="rechargeModal" tabindex="-1" aria-labelledby="rechargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="background: var(--glass-bg); backdrop-filter: blur(20px); border: 1px solid var(--glass-border);">
            <form id="recharge-form" action="{{ url('/user/recharge-money') }}" method="post" novalidate>
                @csrf
                <div class="modal-header border-0">
                    <h5 class="modal-title text-white" id="rechargeModalLabel">
                        <i class="fas fa-wallet me-2"></i>余额充值
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>

                <div class="modal-body">
                    <!-- 充值优惠活动 -->
                    @if($recharge_promotion && count($recharge_promotion) > 0)
                    <div class="mb-4">
                        <h6 class="text-white mb-3">
                            <i class="fas fa-gift me-2"></i>充值优惠
                        </h6>
                        <div class="row g-2">
                            @foreach($recharge_promotion as $key => $item)
                            <div class="col-6">
                                <div class="quick-action-card recharge-promo" data-amount="{{ $item['amount'] }}">
                                    <div class="text-primary fw-bold">充值 ¥{{ $item['amount'] }}</div>
                                    <small class="text-success">送 ¥{{ $item['value'] }}</small>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- 充值金额 -->
                    <div class="mb-4">
                        <label class="form-label text-white mb-2">
                            <i class="fas fa-money-bill me-2"></i>充值金额
                            <span class="text-danger">*</span>
                        </label>
                        <input type="number" name="amount" min="1" class="form-control-glass"
                               placeholder="请输入充值金额" required>
                        <div class="invalid-feedback">请输入有效的充值金额!</div>
                    </div>

                    <!-- 支付方式 -->
                    <div class="mb-4">
                        <label class="form-label text-white mb-3">
                            <i class="fas fa-credit-card me-2"></i>支付方式
                            <span class="text-danger">*</span>
                        </label>
                        <div class="row g-2">
                            @foreach($payways as $key => $way)
                            <div class="col-6">
                                <div class="payment-method-card" data-payment="{{ $way['id'] }}">
                                    <input type="radio" name="payway" value="{{ $way['id'] }}"
                                           class="d-none" id="payway-{{ $key }}"
                                           @if($key == 0) checked @endif required>
                                    <div class="text-center">
                                        <div class="mb-2">
                                            @if($way['pay_check'] == 'alipay')
                                                <i class="fab fa-alipay fa-2x text-primary"></i>
                                            @elseif($way['pay_check'] == 'wxpay')
                                                <i class="fab fa-weixin fa-2x text-success"></i>
                                            @else
                                                <i class="fas fa-credit-card fa-2x text-info"></i>
                                            @endif
                                        </div>
                                        <small class="text-white">{{ $way['pay_name'] }}</small>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>

                <div class="modal-footer border-0">
                    <button type="button" class="btn btn-outline-light" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-gradient" id="rechargeSubmitBtn">
                        <i class="fas fa-wallet me-1"></i>立即充值
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
    </div>
</div>
@endsection

@section('footer')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 显示成功/错误消息
    @if(session('success'))
    showToast('{{ session('success') }}', 'success');
    @endif

    @if($errors->any())
    showToast('{{ $errors->first() }}', 'error');
    @endif

    // 充值优惠活动点击
    document.querySelectorAll('.recharge-promo').forEach(promo => {
        promo.addEventListener('click', function() {
            const amount = this.dataset.amount;
            document.querySelector('input[name="amount"]').value = amount;

            // 移除其他选中状态
            document.querySelectorAll('.recharge-promo').forEach(p => p.classList.remove('selected'));
            this.classList.add('selected');
        });
    });

    // 支付方式选择
    document.querySelectorAll('.payment-method-card').forEach(card => {
        card.addEventListener('click', function() {
            const paymentId = this.dataset.payment;

            // 移除所有选中状态
            document.querySelectorAll('.payment-method-card').forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');

            // 选中对应的radio
            document.querySelector(`input[value="${paymentId}"]`).checked = true;
        });
    });

    // 初始化第一个支付方式为选中状态
    if (document.querySelector('.payment-method-card')) {
        document.querySelector('.payment-method-card').classList.add('selected');
    }

    // 充值表单提交
    const rechargeForm = document.getElementById('recharge-form');
    if (rechargeForm) {
        rechargeForm.addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('rechargeSubmitBtn');
            const amount = document.querySelector('input[name="amount"]').value;

            if (!amount || parseFloat(amount) <= 0) {
                e.preventDefault();
                showToast('请输入有效的充值金额', 'error');
                return false;
            }

            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>处理中...';

            // 验证表单
            if (!this.checkValidity()) {
                e.preventDefault();
                e.stopPropagation();

                // 重置按钮状态
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-wallet me-1"></i>立即充值';
            }

            this.classList.add('was-validated');
        });
    }

    // 快捷操作卡片悬停效果
    document.querySelectorAll('.quick-action-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    console.log('Enterprise User Center Page initialized successfully');
});
</script>
@endsection

@stop

@section('js')
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if(session('success'))
    $.NotificationApp.send("Success", "{{ session('success') }}", "top-right", "rgba(0,0,0,0.2)", "success");
    @endif

    @if($errors->any())
    var firstError = "{{ $errors->all()[0] }}";
    $.NotificationApp.send("Error", firstError, "top-right", "rgba(0,0,0,0.2)", "error");
    @endif
});

$(document).ready(function() {
    // 支付方式标签点击事件
    $('.payments').click(function() {
        $('.payments').removeClass('active');
        $(this).addClass('active');
        $('#paymentGroup input[name="payway"]').val($(this).data('id'));
    });
    
    // 充值金额标签点击事件
    $('.tag').click(function() {
        $('.tag').removeClass('active');
        $(this).toggleClass("active");
        $('input[name=amount]').val($(this).data('amount'));
    });
    
    // 支付方式初始化
    updateSubmitButtonState();
    
    // 更新提交按钮状态
    function updateSubmitButtonState() {
        if ($('input[name="payway"]').val() == 0 || $('input[name="payway"]').val() == "") {
            $('#submit').prop('disabled', true).addClass('btn-disabled');
        } else {
            $('#submit').prop('disabled', false).removeClass('btn-disabled');
        }
    }
    
    // 提交按钮点击验证
    $('#submit').click(function() {
        if ($("input[name='amount']").val() <= 0) {
            $.NotificationApp.send("警告！", "请输入正确的金额~", "top-center", "rgba(0,0,0,0.2)", "info");
            return false;
        }
    });
});
</script>
@stop
