<!-- 企业级商品卡片组件 -->
<div class="col goods-item" data-name="{{ $goods['gd_name'] }}" data-group="{{ $group['gp_name'] ?? '' }}">
    <div class="product-card glass-card card-hover h-100 position-relative overflow-hidden">
        <!-- 商品状态标签 -->
        <div class="position-absolute top-0 start-0 z-3 p-3">
            <div class="d-flex flex-column gap-2">
                <!-- 发货方式标签 -->
                @if ($goods['type'] == \App\Models\Goods::AUTOMATIC_DELIVERY)
                    <span class="badge bg-success d-flex align-items-center">
                        <i class="fas fa-bolt me-1"></i>自动发货
                    </span>
                @else
                    <span class="badge bg-info d-flex align-items-center">
                        <i class="fas fa-user me-1"></i>人工发货
                    </span>
                @endif

                <!-- 库存状态标签 -->
                @if($goods['in_stock'] <= 0)
                    <span class="badge bg-danger d-flex align-items-center">
                        <i class="fas fa-times-circle me-1"></i>缺货
                    </span>
                @elseif($goods['in_stock'] <= 10)
                    <span class="badge bg-warning d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-1"></i>库存紧张
                    </span>
                @else
                    <span class="badge bg-success d-flex align-items-center">
                        <i class="fas fa-check-circle me-1"></i>现货
                    </span>
                @endif
            </div>
        </div>

        <!-- 商品图片 -->
        <div class="position-relative overflow-hidden">
            <a href="@if($goods['in_stock'] > 0) {{ url("/buy/{$goods['id']}") }} @else javascript:void(0); @endif"
               class="d-block text-decoration-none"
               @if($goods['in_stock'] <= 0)
                  onclick="showToast('商品库存不足，请联系客服补货', 'error'); return false;"
               @endif>
                <div class="product-image-container position-relative">
                    <img src="{{ picture_url($goods['picture']) }}"
                         alt="{{ $goods['gd_name'] }}"
                         class="product-image w-100 glow-effect"
                         style="height: 200px; object-fit: cover; border-radius: 12px 12px 0 0;">

                    <!-- 悬停遮罩 -->
                    <div class="product-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center opacity-0 transition-all"
                         style="background: rgba(102, 126, 234, 0.8); border-radius: 12px 12px 0 0;">
                        <div class="text-center text-white">
                            @if($goods['in_stock'] > 0)
                                <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                <p class="mb-0 fw-bold">立即购买</p>
                            @else
                                <i class="fas fa-ban fa-2x mb-2"></i>
                                <p class="mb-0 fw-bold">暂时缺货</p>
                            @endif
                        </div>
                    </div>
                </div>
            </a>
        </div>

        <!-- 商品信息 -->
        <div class="p-4 text-white">
            <!-- 商品标题 -->
            <h5 class="mb-3 fw-bold text-white">
                <a href="@if($goods['in_stock'] > 0) {{ url("/buy/{$goods['id']}") }} @else javascript:void(0); @endif"
                   class="text-decoration-none text-white"
                   @if($goods['in_stock'] <= 0)
                      onclick="showToast('商品库存不足，请联系客服补货', 'error'); return false;"
                   @endif>
                    {{ $goods['gd_name'] }}
                </a>
            </h5>

            <!-- 商品描述 -->
            @if(!empty($goods['gd_description']))
            <p class="text-light mb-3 small">
                {{ Str::limit($goods['gd_description'], 60) }}
            </p>
            @endif

            <!-- 价格信息 -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <span class="price-tag h5 mb-0">
                        ¥{{ number_format($goods['actual_price'], 2) }}
                    </span>
                    @if($goods['original_price'] > $goods['actual_price'])
                        <span class="text-muted text-decoration-line-through ms-2 small">
                            ¥{{ number_format($goods['original_price'], 2) }}
                        </span>
                    @endif
                </div>
                @if($goods['original_price'] > $goods['actual_price'])
                    <span class="badge bg-danger">
                        省¥{{ number_format($goods['original_price'] - $goods['actual_price'], 2) }}
                    </span>
                @endif
            </div>

            <!-- 库存进度条 -->
            @php
                $maxStock = 50;
                $inStock = (int) $goods['in_stock'];
                $percent = 0;
                if($inStock > 0){
                    $percent = ($inStock >= $maxStock) ? 100 : round(($inStock / $maxStock) * 100, 2);
                }
            @endphp
            <div class="mb-4">
                <div class="d-flex justify-content-between mb-2">
                    <span class="small text-light">
                        <i class="fas fa-box me-1"></i>库存
                    </span>
                    <span class="small text-light">{{ $inStock }}</span>
                </div>
                <div class="progress" style="height: 8px; background: rgba(255,255,255,0.1);">
                    <div class="progress-bar progress-glow"
                         style="width: {{ $percent }}%"></div>
                </div>
            </div>

            <!-- 购买按钮 -->
            @if($goods['in_stock'] > 0)
                <a href="{{ url("/buy/{$goods['id']}") }}"
                   class="btn btn-gradient w-100 py-3 fw-bold">
                    <i class="fas fa-shopping-cart me-2"></i>立即购买
                </a>
            @else
                <button class="btn btn-secondary w-100 py-3 fw-bold" disabled>
                    <i class="fas fa-times me-2"></i>暂时缺货
                </button>
            @endif

            <!-- 商品特性标签 -->
            <div class="mt-3 d-flex flex-wrap gap-2">
                @if($goods['type'] == \App\Models\Goods::AUTOMATIC_DELIVERY)
                    <span class="badge bg-primary">
                        <i class="fas fa-bolt me-1"></i>极速发货
                    </span>
                @endif
                @if($goods['in_stock'] > 100)
                    <span class="badge bg-success">
                        <i class="fas fa-warehouse me-1"></i>库存充足
                    </span>
                @endif
                @if($goods['original_price'] > $goods['actual_price'])
                    <span class="badge bg-warning">
                        <i class="fas fa-fire me-1"></i>限时优惠
                    </span>
                @endif
            </div>
        </div>
    </div>
</div>