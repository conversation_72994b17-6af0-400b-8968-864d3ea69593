<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-bs-theme="auto" class="h-100">
<head>
    <!-- Essential Meta Tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no">
    <meta name="description" content="{{ dujiaoka_config_get('seo_description', '专业的企业级数字商品销售平台，提供安全、快速、可靠的购买体验，支持多种支付方式，7x24小时自动发货服务') }}">
    <meta name="keywords" content="{{ dujiaoka_config_get('seo_keywords', '数字商品,礼品卡,充值卡,游戏点卡,软件激活码,企业级,安全支付,自动发货') }}">
    <meta name="author" content="{{ dujiaoka_config_get('text_logo', 'Cursor续杯系统') }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="robots" content="index, follow">
    <meta name="theme-color" content="#0a0e27">
    <meta name="msapplication-TileColor" content="#0a0e27">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@if(!empty($page_title)) {{ $page_title }} - {{ dujiaoka_config_get('text_logo') }} @else {{ dujiaoka_config_get('text_logo') }} - 企业级数字商城系统 @endif">
    <meta property="og:description" content="{{ dujiaoka_config_get('seo_description', '专业的企业级数字商品销售平台，提供安全、快速、可靠的购买体验') }}">
    <meta property="og:image" content="{{ picture_url(dujiaoka_config_get('img_logo')) }}">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:site_name" content="{{ dujiaoka_config_get('text_logo') }}">
    <meta property="og:locale" content="{{ str_replace('_', '-', app()->getLocale()) }}">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@if(!empty($page_title)) {{ $page_title }} - {{ dujiaoka_config_get('text_logo') }} @else {{ dujiaoka_config_get('text_logo') }} - 企业级数字商城 @endif">
    <meta name="twitter:description" content="{{ dujiaoka_config_get('seo_description') }}">
    <meta name="twitter:image" content="{{ picture_url(dujiaoka_config_get('img_logo')) }}">
    <meta name="twitter:site" content="@{{ dujiaoka_config_get('text_logo') }}">
    <meta name="twitter:creator" content="@{{ dujiaoka_config_get('text_logo') }}">

    <!-- Page Title with Enhanced SEO -->
    <title>@if(!empty($page_title)) {{ $page_title }} - {{ dujiaoka_config_get('text_logo') }} @else {{ dujiaoka_config_get('text_logo') }} - 企业级数字商城系统 | 专业安全的数字商品销售平台 @endif</title>

    <!-- Favicons and App Icons -->
    <link rel="icon" type="image/x-icon" href="{{ picture_url(dujiaoka_config_get('img_logo')) }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ picture_url(dujiaoka_config_get('img_logo')) }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ picture_url(dujiaoka_config_get('img_logo')) }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ picture_url(dujiaoka_config_get('img_logo')) }}">
    <link rel="manifest" href="/assets/neon/manifest.json">
    <link rel="mask-icon" href="{{ picture_url(dujiaoka_config_get('img_logo')) }}" color="#667eea">

    <!-- DNS Prefetch for Performance Optimization -->
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link rel="dns-prefetch" href="//unpkg.com">
    <link rel="dns-prefetch" href="//stackpath.bootstrapcdn.com">

    <!-- Preconnect for Critical Resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>

    <!-- Preload Critical Resources for Performance -->
    <link rel="preload" href="/assets/neon/css/theme.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="/assets/neon/css/enterprise.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="/assets/neon/css/neon-effects.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="/assets/neon/css/theme-colors.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="/assets/neon/js/enterprise.js" as="script">
    <link rel="preload" href="/assets/neon/js/neon-components.js" as="script">
    <link rel="preload" href="/assets/neon/fonts/inter-variable-latin.woff2" as="font" type="font/woff2" crossorigin>

    <!-- Critical CSS Inline for Performance and Above-the-fold Content -->
    <style>
        /* Critical CSS for immediate rendering and performance optimization */
        :root {
            /* Enterprise Color Palette */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --primary-gradient-hover: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

            /* Background Gradients */
            --dark-gradient: linear-gradient(135deg, #0a0e27 0%, #1a1e3a 100%);
            --dark-gradient-alt: linear-gradient(135deg, #1a1e3a 0%, #2a2e4a 100%);
            --dark-gradient-deep: linear-gradient(135deg, #0a0e27 0%, #1a1e3a 50%, #2a2e4a 100%);
            --light-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

            /* Glass Morphism Effects */
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-bg-strong: rgba(255, 255, 255, 0.15);
            --glass-bg-light: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-border-strong: rgba(255, 255, 255, 0.3);

            /* Shadow and Glow Effects */
            --shadow-glow: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-glow-strong: 0 12px 40px rgba(31, 38, 135, 0.5);
            --shadow-neon: 0 0 20px rgba(102, 126, 234, 0.5);
            --shadow-neon-strong: 0 0 40px rgba(102, 126, 234, 0.8);
            --shadow-enterprise: 0 25px 50px rgba(0, 0, 0, 0.25);

            /* Typography System */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            --font-display: 'Inter', system-ui, sans-serif;

            /* Spacing and Sizing */
            --border-radius: 16px;
            --border-radius-sm: 8px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

            /* Z-Index Scale */
            --z-dropdown: 1000;
            --z-sticky: 1020;
            --z-fixed: 1030;
            --z-modal-backdrop: 1040;
            --z-modal: 1050;
            --z-popover: 1060;
            --z-tooltip: 1070;
            --z-toast: 1080;
            --z-loading: 9999;
        }

        /* Base Styles for Immediate Rendering */
        * {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
            font-feature-settings: "kern" 1;
            font-kerning: normal;
            height: 100%;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: var(--font-primary);
            background: var(--dark-gradient);
            min-height: 100vh;
            overflow-x: hidden;
            line-height: 1.6;
            color: #ffffff;
            position: relative;
            font-size: 16px;
            font-weight: 400;
            letter-spacing: -0.01em;
        }

        /* Enhanced Loading Screen with Enterprise Branding */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--dark-gradient);
            z-index: var(--z-loading);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            transition: opacity 0.5s ease, visibility 0.5s ease;
        }

        .loading-screen.fade-out {
            opacity: 0;
            visibility: hidden;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .loading-spinner::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            border: 2px solid transparent;
            border-top: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            animation: spin 2s linear infinite reverse;
        }

        .loading-text {
            color: #ffffff;
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .loading-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.875rem;
            font-weight: 400;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Critical Performance Optimizations */
        img {
            max-width: 100%;
            height: auto;
            display: block;
        }

        a {
            text-decoration: none;
            color: inherit;
            transition: var(--transition-fast);
        }

        button {
            border: none;
            background: none;
            cursor: pointer;
            font-family: inherit;
            transition: var(--transition-fast);
        }

        /* Accessibility Improvements */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }

            html {
                scroll-behavior: auto;
            }
        }

        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
            :root {
                --glass-bg: rgba(255, 255, 255, 0.2);
                --glass-border: rgba(255, 255, 255, 0.4);
            }
        }

        /* Dark Mode Enhancements */
        @media (prefers-color-scheme: dark) {
            body {
                background: var(--dark-gradient-deep);
            }
        }
    </style>

    <!-- External CDN Resources with Enhanced Fallbacks and Performance -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossorigin="anonymous" referrerpolicy="no-referrer">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
          integrity="sha512-c42qTSw/wPZ3/5LBzD+Bw5f7bSF2oxou6wEb+I/lqeaKV5FDIfMvvRp772y4jcJLKuGUOpbJMdg/BTl50fJYAw=="
          crossorigin="anonymous" referrerpolicy="no-referrer">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap">

    <!-- Swiper CSS -->
    <link href="/assets/neon/css/swiper-bundle.min.css" rel="stylesheet">

    <!-- Core Theme CSS Files -->
    <link href="/assets/neon/css/theme.min.css" rel="stylesheet">
    <link href="/assets/neon/css/main.css" rel="stylesheet">
    <link href="/assets/neon/css/style.css" rel="stylesheet">
    <link href="/assets/neon/css/styles.css" rel="stylesheet">

    <!-- Enterprise CSS Framework -->
    <link href="/assets/neon/css/enterprise.css" rel="stylesheet">

    <!-- Neon Effects and Visual Enhancements -->
    <link href="/assets/neon/css/neon-effects.css" rel="stylesheet">

    <!-- Theme and Color System -->
    <link href="/assets/neon/css/theme-colors.css" rel="stylesheet">

    <!-- Icons -->
    <link href="/assets/neon/css/icons.min.css" rel="stylesheet">
    <link href="/assets/neon/icons/cartzilla-icons.min.css" rel="stylesheet">

    <!-- Additional Libraries -->
    <link href="/assets/neon/css/choices.min.css" rel="stylesheet">
    <link href="/assets/neon/css/glightbox.min.css" rel="stylesheet">
    <link href="/assets/neon/css/simplebar.min.css" rel="stylesheet">

    <!-- Enterprise Custom Styles and Advanced CSS Framework -->
    <style>
        /* Advanced Enterprise CSS Variables and Design System */
        :root {
            /* Enhanced Primary Color Palette */
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --primary-gradient-hover: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            --primary-gradient-active: linear-gradient(135deg, #4e5bc7 0%, #5e377e 100%);
            --primary-gradient-light: linear-gradient(135deg, #7a8cff 0%, #8a5fb8 100%);
            --primary-gradient-dark: linear-gradient(135deg, #525fd3 0%, #62378c 100%);

            /* Secondary Color Gradients */
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --secondary-gradient-hover: linear-gradient(135deg, #ed7ff7 0%, #f34358 100%);
            --secondary-gradient-light: linear-gradient(135deg, #f5a7ff 0%, #f76b80 100%);

            /* Status Color Gradients */
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --success-gradient-hover: linear-gradient(135deg, #3b98f1 0%, #00def1 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --warning-gradient-hover: linear-gradient(135deg, #ffe4bf 0%, #faa88c 100%);
            --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            --danger-gradient-hover: linear-gradient(135deg, #ff868b 0%, #fdbce2 100%);
            --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --info-gradient-hover: linear-gradient(135deg, #95e9e6 0%, #fdc3d6 100%);

            /* Advanced Background Gradients */
            --dark-gradient: linear-gradient(135deg, #0a0e27 0%, #1a1e3a 100%);
            --dark-gradient-alt: linear-gradient(135deg, #1a1e3a 0%, #2a2e4a 100%);
            --dark-gradient-deep: linear-gradient(135deg, #0a0e27 0%, #1a1e3a 50%, #2a2e4a 100%);
            --dark-gradient-radial: radial-gradient(ellipse at center, #1a1e3a 0%, #0a0e27 100%);
            --light-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            --light-gradient-alt: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);

            /* Animated Gradients */
            --animated-gradient-1: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
            --animated-gradient-2: linear-gradient(45deg, #4facfe, #00f2fe, #a8edea, #fed6e3);
            --animated-gradient-3: linear-gradient(45deg, #ffecd2, #fcb69f, #ff9a9e, #fecfef);

            /* Enhanced Glass Morphism Effects */
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-bg-strong: rgba(255, 255, 255, 0.15);
            --glass-bg-light: rgba(255, 255, 255, 0.05);
            --glass-bg-ultra: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-border-strong: rgba(255, 255, 255, 0.3);
            --glass-border-light: rgba(255, 255, 255, 0.1);
            --glass-backdrop-blur: blur(20px);
            --glass-backdrop-blur-strong: blur(30px);
            --glass-backdrop-blur-light: blur(10px);

            /* Advanced Shadow and Glow Effects */
            --shadow-glow: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-glow-strong: 0 12px 40px rgba(31, 38, 135, 0.5);
            --shadow-glow-ultra: 0 20px 60px rgba(31, 38, 135, 0.7);
            --shadow-neon: 0 0 20px rgba(102, 126, 234, 0.5);
            --shadow-neon-strong: 0 0 40px rgba(102, 126, 234, 0.8);
            --shadow-neon-ultra: 0 0 60px rgba(102, 126, 234, 1);
            --shadow-enterprise: 0 25px 50px rgba(0, 0, 0, 0.25);
            --shadow-enterprise-strong: 0 35px 70px rgba(0, 0, 0, 0.4);
            --shadow-inset: inset 0 2px 4px rgba(0, 0, 0, 0.1);
            --shadow-inset-strong: inset 0 4px 8px rgba(0, 0, 0, 0.2);

            /* Enhanced Typography System */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            --font-display: 'Inter', system-ui, sans-serif;
            --font-heading: 'Inter', system-ui, sans-serif;
            --font-body: 'Inter', system-ui, sans-serif;

            /* Font Weights */
            --font-weight-thin: 100;
            --font-weight-light: 300;
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
            --font-weight-extrabold: 800;
            --font-weight-black: 900;

            /* Font Sizes */
            --font-size-xs: 0.75rem;
            --font-size-sm: 0.875rem;
            --font-size-base: 1rem;
            --font-size-lg: 1.125rem;
            --font-size-xl: 1.25rem;
            --font-size-2xl: 1.5rem;
            --font-size-3xl: 1.875rem;
            --font-size-4xl: 2.25rem;
            --font-size-5xl: 3rem;
            --font-size-6xl: 3.75rem;
            --font-size-7xl: 4.5rem;
            --font-size-8xl: 6rem;
            --font-size-9xl: 8rem;

            /* Enhanced Spacing and Sizing */
            --border-radius: 16px;
            --border-radius-xs: 4px;
            --border-radius-sm: 8px;
            --border-radius-md: 12px;
            --border-radius-lg: 24px;
            --border-radius-xl: 32px;
            --border-radius-2xl: 40px;
            --border-radius-3xl: 48px;
            --border-radius-full: 9999px;

            /* Spacing Scale */
            --spacing-0: 0;
            --spacing-1: 0.25rem;
            --spacing-2: 0.5rem;
            --spacing-3: 0.75rem;
            --spacing-4: 1rem;
            --spacing-5: 1.25rem;
            --spacing-6: 1.5rem;
            --spacing-8: 2rem;
            --spacing-10: 2.5rem;
            --spacing-12: 3rem;
            --spacing-16: 4rem;
            --spacing-20: 5rem;
            --spacing-24: 6rem;
            --spacing-32: 8rem;
            --spacing-40: 10rem;
            --spacing-48: 12rem;
            --spacing-56: 14rem;
            --spacing-64: 16rem;

            /* Advanced Transition System */
            --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            --transition-elastic: 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            --transition-spring: 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

            /* Easing Functions */
            --ease-in: cubic-bezier(0.4, 0, 1, 1);
            --ease-out: cubic-bezier(0, 0, 0.2, 1);
            --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
            --ease-in-back: cubic-bezier(0.6, -0.28, 0.735, 0.045);
            --ease-out-back: cubic-bezier(0.175, 0.885, 0.32, 1.275);
            --ease-in-out-back: cubic-bezier(0.68, -0.55, 0.265, 1.55);

            /* Enhanced Z-Index Scale */
            --z-hide: -1;
            --z-auto: auto;
            --z-base: 0;
            --z-docked: 10;
            --z-dropdown: 1000;
            --z-sticky: 1020;
            --z-banner: 1030;
            --z-overlay: 1040;
            --z-modal: 1050;
            --z-popover: 1060;
            --z-skiplink: 1070;
            --z-toast: 1080;
            --z-tooltip: 1090;
            --z-loading: 9999;
        }

        /* Base Styles */
        * {
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body {
            background: var(--dark-gradient);
            min-height: 100vh;
            font-family: var(--font-primary);
            line-height: 1.6;
            color: #ffffff;
            overflow-x: hidden;
            position: relative;
        }

        /* Enhanced Glass Morphism Components */
        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-glow);
            border-radius: var(--border-radius);
            transition: var(--transition-normal);
        }

        .glass-card-strong {
            background: var(--glass-bg-strong);
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);
            border: 1px solid var(--glass-border-strong);
            box-shadow: var(--shadow-glow-strong);
        }

        .glass-card-light {
            background: var(--glass-bg-light);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
        }

        /* Enhanced Glow Effects */
        .glow-effect {
            animation: glow 2s ease-in-out infinite alternate;
        }

        .glow-effect-strong {
            animation: glowStrong 2s ease-in-out infinite alternate;
        }

        .glow-effect-pulse {
            animation: glowPulse 3s ease-in-out infinite;
        }

        @keyframes glow {
            from {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
                transform: scale(1);
            }
            to {
                box-shadow: 0 0 30px rgba(102, 126, 234, 0.8);
                transform: scale(1.02);
            }
        }

        @keyframes glowStrong {
            from {
                box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
                filter: brightness(1);
            }
            to {
                box-shadow: 0 0 50px rgba(102, 126, 234, 1);
                filter: brightness(1.1);
            }
        }

        @keyframes glowPulse {
            0%, 100% {
                box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
                opacity: 1;
            }
            50% {
                box-shadow: 0 0 40px rgba(102, 126, 234, 0.8);
                opacity: 0.9;
            }
        }

        /* Enhanced Gradient Text */
        .gradient-text {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            position: relative;
        }

        .gradient-text-secondary {
            background: var(--secondary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .gradient-text-success {
            background: var(--success-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Enhanced Button Styles */
        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            border-radius: var(--border-radius-sm);
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .btn-gradient::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: var(--transition-slow);
        }

        .btn-gradient:hover::before {
            left: 100%;
        }

        .btn-gradient:hover {
            background: var(--primary-gradient-hover);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-gradient:active {
            transform: translateY(0);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        /* Loading and Animation Styles */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        .loading-spinner-large {
            width: 40px;
            height: 40px;
            border-width: 4px;
        }

        .loading-dots {
            display: inline-flex;
            gap: 4px;
        }

        .loading-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--primary-gradient);
            animation: loadingDots 1.4s ease-in-out infinite both;
        }

        .loading-dots span:nth-child(1) { animation-delay: -0.32s; }
        .loading-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes loadingDots {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Navigation Styles */
        .navbar-glass {
            background: rgba(10, 14, 39, 0.9);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            transition: var(--transition-normal);
        }

        .navbar-glass.scrolled {
            background: rgba(10, 14, 39, 0.95);
            box-shadow: var(--shadow-glow);
        }

        .nav-link-glass {
            color: rgba(255, 255, 255, 0.8);
            transition: var(--transition-fast);
            position: relative;
            padding: 8px 16px;
            border-radius: var(--border-radius-sm);
        }

        .nav-link-glass::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary-gradient);
            transition: var(--transition-normal);
            transform: translateX(-50%);
        }

        .nav-link-glass:hover,
        .nav-link-glass.active {
            color: white;
            background: var(--glass-bg-light);
        }

        .nav-link-glass:hover::before,
        .nav-link-glass.active::before {
            width: 80%;
        }

        /* Footer Styles */
        .footer-glass {
            background: rgba(10, 14, 39, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-top: 1px solid var(--glass-border);
        }

        /* Card and Component Styles */
        .card-hover {
            transition: var(--transition-normal);
            cursor: pointer;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-glow-strong);
        }

        .card-enterprise {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            padding: 2rem;
            transition: var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .card-enterprise::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--primary-gradient);
            opacity: 0;
            transition: var(--transition-normal);
        }

        .card-enterprise:hover::before {
            opacity: 1;
        }

        .card-enterprise:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-glow-strong);
            border-color: var(--glass-border-strong);
        }

        /* Text and Typography Enhancements */
        .text-shadow {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .text-shadow-strong {
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.7);
        }

        .text-glow {
            text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
        }

        /* Background Patterns */
        .bg-pattern {
            background-image:
                radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
        }

        .bg-pattern-dots {
            background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
            background-size: 20px 20px;
        }

        .bg-pattern-grid {
            background-image:
                linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
            background-size: 20px 20px;
        }

        /* Status Indicators */
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            position: relative;
        }

        .status-indicator.large {
            width: 12px;
            height: 12px;
        }

        .status-online {
            background-color: #10b981;
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
        }

        .status-offline {
            background-color: #ef4444;
            box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
        }

        .status-pending {
            background-color: #f59e0b;
            box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
        }

        .status-processing {
            background-color: #3b82f6;
            box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }

        /* Pulse Animation */
        .pulse {
            animation: pulse 2s infinite;
        }

        .pulse-fast {
            animation: pulse 1s infinite;
        }

        .pulse-slow {
            animation: pulse 3s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.05);
            }
        }
    </style>

    <!-- Page-specific Header Content -->
    @yield('header')

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "{{ dujiaoka_config_get('text_logo', 'Cursor续杯系统') }}",
        "description": "{{ dujiaoka_config_get('seo_description', '专业的企业级数字商品销售平台') }}",
        "url": "{{ url('/') }}",
        "logo": "{{ picture_url(dujiaoka_config_get('img_logo')) }}",
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+86-************",
            "contactType": "customer service",
            "availableLanguage": ["Chinese", "English"]
        },
        "sameAs": [
            "https://t.me/riniba"
        ]
    }
    </script>
</head>
<body class="bg-pattern enterprise-body" data-theme="dark" data-loading="true">
    <!-- Enhanced Loading Screen with Enterprise Branding -->
    <div id="loading-screen" class="loading-screen" role="status" aria-label="页面加载中">
        <div class="loading-container">
            <!-- Enterprise Logo -->
            <div class="loading-logo mb-4">
                <img src="{{ picture_url(dujiaoka_config_get('img_logo')) }}"
                     alt="{{ dujiaoka_config_get('text_logo') }}"
                     class="loading-logo-img">
            </div>

            <!-- Loading Spinner with Multiple Rings -->
            <div class="loading-spinner-container">
                <div class="loading-spinner"></div>
                <div class="loading-spinner-inner"></div>
            </div>

            <!-- Loading Text with Animation -->
            <div class="loading-text-container">
                <h5 class="loading-text">{{ dujiaoka_config_get('text_logo', 'Cursor续杯系统') }}</h5>
                <p class="loading-subtitle">企业级数字商城正在为您加载...</p>
                <div class="loading-progress">
                    <div class="loading-progress-bar" id="loadingProgress"></div>
                </div>
            </div>

            <!-- Loading Tips -->
            <div class="loading-tips mt-4">
                <p class="loading-tip" id="loadingTip">正在初始化系统...</p>
            </div>
        </div>
    </div>

    <!-- Performance Monitor (Development Only) -->
    @if(config('app.debug'))
    <div id="performance-monitor" class="position-fixed bottom-0 start-0 p-2 bg-dark text-white small" style="z-index: 10000; opacity: 0.7;">
        <div>加载时间: <span id="load-time">-</span>ms</div>
        <div>DOM节点: <span id="dom-nodes">-</span></div>
        <div>内存使用: <span id="memory-usage">-</span>MB</div>
    </div>
    @endif

    <!-- Accessibility Skip Links -->
    <div class="skip-links">
        <a href="#main-content" class="skip-link">跳转到主要内容</a>
        <a href="#main-navigation" class="skip-link">跳转到导航菜单</a>
        <a href="#footer-content" class="skip-link">跳转到页脚</a>
    </div>

    <!-- Enhanced Theme Switcher with More Options -->
    <div class="position-fixed top-0 end-0 p-3" style="z-index: var(--z-sticky);">
        <div class="theme-switcher-container">
            <div class="dropdown">
                <button class="btn btn-outline-light btn-sm dropdown-toggle glass-card theme-switcher-btn"
                        type="button"
                        data-bs-toggle="dropdown"
                        aria-expanded="false"
                        title="切换主题">
                    <i class="fas fa-palette me-1"></i>
                    <span class="d-none d-md-inline">主题</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end glass-card border-0 theme-dropdown">
                    <li class="dropdown-header text-white-50">
                        <i class="fas fa-swatchbook me-2"></i>选择主题
                    </li>
                    <li><hr class="dropdown-divider border-light opacity-25"></li>
                    <li>
                        <button class="dropdown-item text-white theme-option" data-bs-theme-value="light">
                            <i class="fas fa-sun me-2 text-warning"></i>
                            <span>浅色模式</span>
                            <i class="fas fa-check ms-auto d-none"></i>
                        </button>
                    </li>
                    <li>
                        <button class="dropdown-item text-white theme-option" data-bs-theme-value="dark">
                            <i class="fas fa-moon me-2 text-info"></i>
                            <span>深色模式</span>
                            <i class="fas fa-check ms-auto d-none"></i>
                        </button>
                    </li>
                    <li>
                        <button class="dropdown-item text-white theme-option" data-bs-theme-value="auto">
                            <i class="fas fa-circle-half-stroke me-2 text-secondary"></i>
                            <span>自动模式</span>
                            <i class="fas fa-check ms-auto d-none"></i>
                        </button>
                    </li>
                    <li><hr class="dropdown-divider border-light opacity-25"></li>
                    <li>
                        <button class="dropdown-item text-white" id="resetTheme">
                            <i class="fas fa-undo me-2 text-danger"></i>
                            <span>重置设置</span>
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Floating Action Buttons -->
    <div class="floating-actions">
        <!-- Back to Top Button -->
        <button class="fab fab-back-to-top glass-card" id="backToTop" title="返回顶部" aria-label="返回顶部">
            <i class="fas fa-arrow-up"></i>
        </button>

        <!-- Customer Service Button -->
        <button class="fab fab-customer-service glass-card" id="customerService" title="客服支持" aria-label="客服支持">
            <i class="fas fa-headset"></i>
        </button>

        <!-- Search Button -->
        <button class="fab fab-search glass-card" id="globalSearch" title="全局搜索" aria-label="全局搜索">
            <i class="fas fa-search"></i>
        </button>
    </div>

    <!-- Background Particles and Effects -->
    <div class="background-effects">
        <div class="particles-container" id="particles"></div>
        <div class="gradient-orbs">
            <div class="gradient-orb gradient-orb-1"></div>
            <div class="gradient-orb gradient-orb-2"></div>
            <div class="gradient-orb gradient-orb-3"></div>
        </div>
    </div>

    <!-- Navigation -->
    @include('neon.layouts._nav')

    <!-- Main Content Area -->
    <main class="main-content" id="main-content" role="main">
        <!-- Breadcrumb Navigation -->
        @if(!Request::is('/'))
        <nav aria-label="breadcrumb" class="breadcrumb-nav">
            <div class="container">
                <ol class="breadcrumb glass-card">
                    <li class="breadcrumb-item">
                        <a href="/" class="text-white-50">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    @if(!empty($page_title))
                    <li class="breadcrumb-item active text-white" aria-current="page">
                        {{ $page_title }}
                    </li>
                    @endif
                </ol>
            </div>
        </nav>
        @endif

        <!-- Page Content -->
        <div class="page-content">
            @yield('content')
        </div>
    </main>

    <!-- Footer -->
    @include('neon.layouts._footer')

    <!-- Scripts -->
    @include('neon.layouts._script')

    <!-- Page-specific Footer Content -->
    @yield('footer')

    <!-- Cookie Consent Banner -->
    <div class="cookie-consent glass-card" id="cookieConsent" style="display: none;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <p class="text-white mb-0">
                        <i class="fas fa-cookie-bite me-2"></i>
                        我们使用Cookie来提供更好的用户体验。继续使用本站即表示您同意我们的
                        <a href="/privacy" class="text-primary">隐私政策</a>。
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light btn-sm me-2" id="cookieDecline">拒绝</button>
                    <button class="btn btn-gradient btn-sm" id="cookieAccept">接受</button>
                </div>
            </div>
        </div>
    </div>

    <!-- PWA Install Prompt -->
    <div class="pwa-install-prompt glass-card" id="pwaInstallPrompt" style="display: none;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="text-white mb-1">
                        <i class="fas fa-mobile-alt me-2"></i>安装应用
                    </h6>
                    <p class="text-white-50 mb-0 small">
                        将{{ dujiaoka_config_get('text_logo') }}添加到主屏幕，获得更好的体验
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light btn-sm me-2" id="pwaDecline">稍后</button>
                    <button class="btn btn-gradient btn-sm" id="pwaInstall">安装</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

