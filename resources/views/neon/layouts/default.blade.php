<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-bs-theme="auto">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="{{ dujiaoka_config_get('seo_description') }}">
    <meta name="keywords" content="{{ dujiaoka_config_get('seo_keywords') }}">
    <meta name="author" content="{{ dujiaoka_config_get('text_logo') }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@if(!empty($page_title)) {{ $page_title }} @else {{ dujiaoka_config_get('text_logo') }} @endif</title>
    <link rel="icon" type="image/x-icon" href="{{ picture_url(dujiaoka_config_get('img_logo')) }}">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="/assets/neon/css/bootstrap.min.css" as="style">
    <link rel="preload" href="/assets/neon/css/theme.min.css" as="style">
    <link rel="preload" href="/assets/neon/js/bootstrap.bundle.min.js" as="script">

    <!-- External CDN Resources -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Bootstrap CSS -->
    <link href="/assets/neon/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/assets/neon/css/theme.min.css" rel="stylesheet">
    <link href="/assets/neon/css/custom.css" rel="stylesheet">
    <link href="/assets/neon/css/enterprise.css" rel="stylesheet">

    <!-- Custom Styles -->
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark-gradient: linear-gradient(135deg, #0a0e27 0%, #1a1e3a 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-glow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }

        body {
            background: var(--dark-gradient);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-glow);
            border-radius: 16px;
        }

        .glow-effect {
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(102, 126, 234, 0.5); }
            to { box-shadow: 0 0 30px rgba(102, 126, 234, 0.8); }
        }

        .gradient-text {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .btn-gradient {
            background: var(--primary-gradient);
            border: none;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .navbar-glass {
            background: rgba(10, 14, 39, 0.9);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-glass {
            background: rgba(10, 14, 39, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .text-shadow {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .bg-pattern {
            background-image:
                radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online { background-color: #10b981; }
        .status-offline { background-color: #ef4444; }
        .status-pending { background-color: #f59e0b; }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>

    @yield('header')
</head>
<body class="bg-pattern">
    <!-- Loading Screen -->
    <div id="loading-screen" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: var(--dark-gradient); z-index: 9999;">
        <div class="text-center text-white">
            <div class="loading-spinner mb-3"></div>
            <h5 class="gradient-text">加载中...</h5>
        </div>
    </div>

    <!-- Theme Switcher -->
    <div class="position-fixed top-0 end-0 z-3 p-3">
        <div class="dropdown">
            <button class="btn btn-outline-light btn-sm dropdown-toggle glass-card" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-palette me-1"></i>主题
            </button>
            <ul class="dropdown-menu glass-card border-0">
                <li><button class="dropdown-item text-white" data-bs-theme-value="light"><i class="fas fa-sun me-2"></i>浅色</button></li>
                <li><button class="dropdown-item text-white" data-bs-theme-value="dark"><i class="fas fa-moon me-2"></i>深色</button></li>
                <li><button class="dropdown-item text-white" data-bs-theme-value="auto"><i class="fas fa-circle-half-stroke me-2"></i>自动</button></li>
            </ul>
        </div>
    </div>

    @include('neon.layouts._nav')

    <main class="min-vh-100">
        @yield('content')
    </main>

    @include('neon.layouts._footer')
    @include('neon.layouts._script')

    @yield('footer')
</body>
</html>

