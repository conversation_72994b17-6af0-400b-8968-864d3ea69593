<?php
/**
 * The file was created by <PERSON><PERSON><PERSON>.
 *
 * <AUTHOR>
 * @copyright assimon<<EMAIL>>
 * @link      http://utf8.hk/
 */

return [
    'official_website' => '官网',
    'dashboard' => '仪表盘',
    'dashboard_description' => '每个梦想的路上，一起前行....！',
    'join_qq_group' => '加入qq群',
    'join_telegram_group' => '加入tg群',
    'is_open' => '是否启用',
    'trashed' => '回收站',
    'restore' => '恢复',
    'are_you_restore_sure' => '确定恢复吗?',
    'restored' => '已恢复',
    'ord' => '数值越大，排名越靠前',
    'status_open' => '启用',
    'status_close' => '禁用',
    'sales_data' => '销售数据',
    'order_success_rate' => '订单成交率',
    'order_count_number' => '总订单数',
    'last_seven_days' => '最近7天',
    'last_today' => '今天',
    'last_month' => '最近一个月',
    'last_year' => '最近一年',

    'status_pending_number' => '待处理订单数',
    'status_processing_number' => '处理中订单数',
    'status_completed_number' => '已完成订单数',
    'status_failure_number' => '失败订单数',
    'status_abnormal_number' => '异常订单数',

    'payment_chart' => '支付数据',
    'payment_successful_number' => '支付成功数',
    'unpaid_number' => '未支付数',

    'sales_chart' => '销售额',
    'popular_goods' => '受欢迎的商品',
    'warning_title' => '请注意！！',
    'error_title' => '出现错误了哟~',
    'callback' => '返回',
    'reset' => '重置',
    'search_now' => '立即查询',

    'home_page' => '首页',
    'group_all' => '全部',
    'order_search' => '订单查询',
    'site_announcement' => '公告',
    'price' => '价格',
    'wholesale_discount' => '批发价',
    'order_now' => '下单',
    'not' => '无',
    'close' => '关闭',
    'discount' => '折',
    'home_discount'  => '折扣',
    'share_qr' => '分享此商品',
    'by_amount' => '购买',
    'or_the_above' => '件或以上',
    'each' => '每个',
    'email' => '邮箱',
    'payment_method' => '支付方式',
    'payment_fee' => '通道费率',
    'search_password' => '订单查询密码',
    'img_verify_code' => '图形验证码',
    'coupon_code' => '优惠码',
    'copy_text' => '复制',
    'search_goods_name' => '商品名称...',
    'preselection' => '可自选卡密，加价',

    'behavior_verification' => '行为验证',
    'click_to_behavior_verification' => '点击进行此处行为验证',
    'success_behavior_verification' => '已完成行为验证',
    'fail_behavior_verification' => '行为验证失败',
    'please_complete_the_behavior_verification_correctly' => '请正确完成行为验证',

    'confirm_order' => '确认订单',
    'date_to_expired_order' => ':min分钟内未完成支付订单将作废',
    'order_information' => '订单资料',
    'pay_immediately' => '立即支付',
    'amount_to_be_paid' => '需要支付金额',
    'open_the_app_to_pay' => '打开 APP 支付',
    'order_search_by_sn' => '订单号查询',
    'order_search_by_email' => '下单邮箱查询',
    'order_search_by_browser' => '浏览器查询',

    'scan_qrcode_to_pay' => '扫码支付',
    'pay_order_expiration_date_prompt' => '请打开 APP 扫码支付！有效期:min分钟',
    'money_symbol' => '￥',
    'purchase_limit' => '每单限',


    'prompt' => [
        'server_illegal_request' => '非法请求！',
        'the_goods_is_not_on_the_shelves' => '该商品未上架！',
        'wholesale_price_format_error' => '批发价设置有误',
        'by_amount_not_null' =>  '购买数量不能为0',
        'inventory_shortage' => '库存不足',
        'please_select_mode_of_payment' => '请选择支付方式',
        'goods_does_not_exist' => '商品不存在',
        'search_password_can_not_be_empty' =>  '请填写订单查询密码',
        'image_verify_code_error' =>  '图形验证码错误',
        'buy_amount_format_error' =>  '请正确填写购买数量',
        'email_format_error' =>  '请正确填写邮箱',
        'geetest_validate_fail' => '行为验证未通过',
        'purchase_limit_exceeded' => '单笔购买数量超过限制',
        'coupon_does_not_exist' => '优惠码不存在',
        'coupon_lack_of_available_opportunities' => '优惠码可使用次数不足',
        'can_not_be_empty' => '不能为空',
        'order_does_not_exist' => '订单不存在',
        'order_is_expired' => '订单已过期，请重新下单',
        'order_already_paid' => '订单已经支付过了，请勿重复支付',
        'order_status_completed' => '订单已经处理',
        'order_inconsistent_amounts' => '订单金额不一致',
        'order_carmis_insufficient_quantity_available' => '库存可使用卡密不足，请联系管理员核查',
        'pay_gateway_does_not_exist' => '支付网关不存在',
        'abnormal_payment_channel' => '支付网关异常！',
        'payment_successful' => '支付成功！',
        'copy_text_success' => '复制成功',
        'copy_text_failed' => '复制失败',
        'search_order_browser_tips' => '最多只能查询最近 5 笔订单',
        'no_related_order_found_for_cache' => '未找到相关订单缓存！',
        'no_related_order_found' => '未找到相关订单！',
        'new_order_push' => '新订单通知',
        'loop_carmis_limit' => '此商品最多购买一件！'
    ],

    'equipment' => [
        'please_use_a_browser_to_open' => '请使用内置浏览器打开',
        'does_not_support_wechat_or_qq_access' => '本站不支持 微信或QQ 内访问',
        'please_follow_the_prompts_to_open' => '请按提示在手机 浏览器 打开',
        'open_browser_tips' => '点击右上角···图标 or 复制网址自行打开',
        'apple_device' => '苹果设备',
        'android_device' => '安卓设备',
        'click_on_the_upper_right_corner' => '点击右上角',
        'open_the_browser' => '在 浏览器 打开',
        'what_do_you_need_today' => '今天需要一点什么？',
        'self_promotion' => '优质的商品和卓越的客户服务代表完美的交易流程体现。',
    ],

    'page-title' => [
        'home' => '首页',
        'bill' => '订单确认',
        'order-detail' => '订单详情',
        'order-search' => '订单查询',
        'article' => '文章'
    ]
];
