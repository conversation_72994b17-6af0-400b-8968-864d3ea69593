<?php

return [
    'labels' => [
        'Order' => '订单',
        'order' => '订单',
    ],
    'fields' => [
        'actual_price' => '实际支付价格',
        'preselection' => '自选卡密',
        'buy_amount' => '购买数量',
        'buy_ip' => '购买者下单IP地址',
        'coupon_discount_price' => '优惠码优惠价格',
        'coupon_id' => '优惠码',
        'email' => '下单邮箱',
        'goods_id' => '所属商品',
        'goods_price' => '商品单价',
        'info' => '订单详情',
        'order_id' => '订单ID',
        'order_sn' => '订单号',
        'pay_id' => '支付通道',
        'status' => '订单状态',
        'search_pwd' => '查询密码',
        'title' => '订单名称',
        'total_price' => '订单总价',
        'trade_no' => '第三方支付订单号',
        'type' => '订单类型',
        'wholesale_discount_price' => '批发价优惠',
        'status_wait_pay' => '待支付',
        'status_pending' => '待处理',
        'status_processing' => '处理中',
        'status_completed' => '已完成',
        'status_failure' => '失败',
        'status_abnormal' => '异常',
        'status_expired' => '已过期',
        'order_created' => '订单创建时间',
        'order_detail' => '订单详情',
    ],
    'options' => [
    ],
];
