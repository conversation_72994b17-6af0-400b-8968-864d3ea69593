/* 
 * Enterprise Neon Theme CSS
 * 企业级霓虹主题样式
 * Version: 2.0.0
 * Author: Enterprise Development Team
 */

/* ===== 全局变量定义 ===== */
:root {
    /* 主色调渐变 */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    
    /* 背景渐变 */
    --dark-gradient: linear-gradient(135deg, #0a0e27 0%, #1a1e3a 100%);
    --light-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --hero-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    
    /* 玻璃态效果 */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-bg-dark: rgba(0, 0, 0, 0.2);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-border-dark: rgba(255, 255, 255, 0.1);
    
    /* 阴影效果 */
    --shadow-glow: 0 8px 32px rgba(31, 38, 135, 0.37);
    --shadow-soft: 0 4px 16px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.15);
    --shadow-hard: 0 12px 32px rgba(0, 0, 0, 0.2);
    
    /* 动画时长 */
    --transition-fast: 0.15s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    
    /* 边框圆角 */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    
    /* 字体大小 */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    
    /* 间距 */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
}

/* ===== 基础样式重置 ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background: var(--dark-gradient);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== 滚动条样式 ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 4px;
    transition: background var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-gradient);
}

/* ===== 选择文本样式 ===== */
::selection {
    background: rgba(102, 126, 234, 0.3);
    color: #ffffff;
}

::-moz-selection {
    background: rgba(102, 126, 234, 0.3);
    color: #ffffff;
}

/* ===== 玻璃态卡片组件 ===== */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-glow);
    transition: all var(--transition-normal);
}

.glass-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
    border-color: rgba(255, 255, 255, 0.3);
}

.glass-card-dark {
    background: var(--glass-bg-dark);
    border: 1px solid var(--glass-border-dark);
}

/* ===== 渐变按钮组件 ===== */
.btn-gradient {
    background: var(--primary-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.btn-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn-gradient:hover::before {
    left: 100%;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.btn-gradient:active {
    transform: translateY(0);
}

.btn-gradient-secondary {
    background: var(--secondary-gradient);
}

.btn-gradient-success {
    background: var(--success-gradient);
}

.btn-gradient-warning {
    background: var(--warning-gradient);
    color: #333;
}

.btn-gradient-danger {
    background: var(--danger-gradient);
}

/* ===== 发光效果 ===== */
.glow-effect {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
    }
    to {
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.8), 0 0 40px rgba(102, 126, 234, 0.6);
    }
}

.glow-text {
    text-shadow: 0 0 10px rgba(102, 126, 234, 0.8);
}

/* ===== 渐变文字 ===== */
.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.gradient-text-secondary {
    background: var(--secondary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===== 加载动画 ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

.loading-spinner-lg {
    width: 40px;
    height: 40px;
    border-width: 4px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== 脉冲动画 ===== */
.pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* ===== 弹跳动画 ===== */
.bounce {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        transform: translate3d(0, -30px, 0);
    }
    70% {
        animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* ===== 导航栏样式 ===== */
.navbar-glass {
    background: rgba(10, 14, 39, 0.9);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.navbar-glass.scrolled {
    background: rgba(10, 14, 39, 0.95);
    box-shadow: var(--shadow-medium);
}

/* ===== 页脚样式 ===== */
.footer-glass {
    background: rgba(10, 14, 39, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== 卡片悬停效果 ===== */
.card-hover {
    transition: all var(--transition-normal);
    cursor: pointer;
}

.card-hover:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-hard);
}

/* ===== 状态指示器 ===== */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: var(--space-2);
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.status-online {
    background-color: #10b981;
}

.status-online::after {
    background-color: #10b981;
}

.status-offline {
    background-color: #ef4444;
}

.status-offline::after {
    background-color: #ef4444;
}

.status-pending {
    background-color: #f59e0b;
}

.status-pending::after {
    background-color: #f59e0b;
}

@keyframes ping {
    75%, 100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* ===== 背景图案 ===== */
.bg-pattern {
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(240, 147, 251, 0.05) 0%, transparent 50%);
    background-size: 100% 100%, 100% 100%, 100% 100%;
    background-repeat: no-repeat;
}

.bg-pattern-animated {
    background-size: 200% 200%, 200% 200%, 200% 200%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%, 100% 50%, 50% 0%;
    }
    50% {
        background-position: 100% 50%, 0% 50%, 50% 100%;
    }
    100% {
        background-position: 0% 50%, 100% 50%, 50% 0%;
    }
}

/* ===== 文字阴影效果 ===== */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.text-shadow-lg {
    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.6);
}

/* ===== 响应式工具类 ===== */
@media (max-width: 768px) {
    .glass-card {
        margin: var(--space-2);
        border-radius: var(--radius-md);
    }
    
    .btn-gradient {
        padding: var(--space-2) var(--space-4);
        font-size: var(--text-sm);
    }
    
    .gradient-text {
        font-size: var(--text-xl);
    }
}

/* ===== 可访问性增强 ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== 高对比度模式 ===== */
@media (prefers-contrast: high) {
    .glass-card {
        border-width: 2px;
        border-color: rgba(255, 255, 255, 0.5);
    }
    
    .btn-gradient {
        border: 2px solid rgba(255, 255, 255, 0.3);
    }
}

/* ===== 打印样式 ===== */
@media print {
    .glass-card {
        background: white;
        color: black;
        border: 1px solid #ccc;
        box-shadow: none;
    }
    
    .btn-gradient {
        background: #333;
        color: white;
    }
    
    .gradient-text {
        color: #333;
        background: none;
        -webkit-text-fill-color: initial;
    }
}
