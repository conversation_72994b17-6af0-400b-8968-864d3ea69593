/**
 * Enterprise Neon Theme JavaScript
 * 企业级霓虹主题脚本
 * Version: 2.0.0
 * Author: Enterprise Development Team
 */

(function() {
    'use strict';

    // ===== 全局配置 =====
    const CONFIG = {
        // API 配置
        api: {
            baseUrl: '/api',
            timeout: 30000,
            retryAttempts: 3
        },
        
        // 动画配置
        animation: {
            duration: 300,
            easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        },
        
        // 主题配置
        theme: {
            storageKey: 'neon_theme',
            defaultTheme: 'dark',
            autoSwitchTime: {
                light: 6, // 6:00 AM
                dark: 18  // 6:00 PM
            }
        },
        
        // 通知配置
        notification: {
            duration: 5000,
            maxVisible: 5,
            position: 'top-right'
        }
    };

    // ===== 工具函数 =====
    const Utils = {
        // 防抖函数
        debounce(func, wait, immediate) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    timeout = null;
                    if (!immediate) func(...args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func(...args);
            };
        },

        // 节流函数
        throttle(func, limit) {
            let inThrottle;
            return function(...args) {
                if (!inThrottle) {
                    func.apply(this, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },

        // 格式化数字
        formatNumber(num) {
            return new Intl.NumberFormat('zh-CN').format(num);
        },

        // 格式化货币
        formatCurrency(amount, currency = 'CNY') {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: currency
            }).format(amount);
        },

        // 格式化日期
        formatDate(date, options = {}) {
            const defaultOptions = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            return new Intl.DateTimeFormat('zh-CN', { ...defaultOptions, ...options }).format(new Date(date));
        },

        // 生成唯一ID
        generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        },

        // 深拷贝
        deepClone(obj) {
            return JSON.parse(JSON.stringify(obj));
        },

        // 检查是否为移动设备
        isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        },

        // 获取元素位置
        getElementPosition(element) {
            const rect = element.getBoundingClientRect();
            return {
                top: rect.top + window.pageYOffset,
                left: rect.left + window.pageXOffset,
                width: rect.width,
                height: rect.height
            };
        },

        // 平滑滚动到元素
        scrollToElement(element, offset = 0) {
            const elementPosition = this.getElementPosition(element);
            window.scrollTo({
                top: elementPosition.top - offset,
                behavior: 'smooth'
            });
        }
    };

    // ===== HTTP 请求类 =====
    class HttpClient {
        constructor(baseUrl = CONFIG.api.baseUrl) {
            this.baseUrl = baseUrl;
            this.timeout = CONFIG.api.timeout;
            this.retryAttempts = CONFIG.api.retryAttempts;
        }

        async request(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                timeout: this.timeout
            };

            // 添加 CSRF Token
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                defaultOptions.headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
            }

            const config = { ...defaultOptions, ...options };
            const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;

            try {
                const response = await fetch(fullUrl, config);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return await response.json();
                }
                
                return await response.text();
            } catch (error) {
                console.error('HTTP Request failed:', error);
                throw error;
            }
        }

        async get(url, params = {}) {
            const queryString = new URLSearchParams(params).toString();
            const fullUrl = queryString ? `${url}?${queryString}` : url;
            return this.request(fullUrl);
        }

        async post(url, data = {}) {
            return this.request(url, {
                method: 'POST',
                body: JSON.stringify(data)
            });
        }

        async put(url, data = {}) {
            return this.request(url, {
                method: 'PUT',
                body: JSON.stringify(data)
            });
        }

        async delete(url) {
            return this.request(url, {
                method: 'DELETE'
            });
        }
    }

    // ===== 通知系统 =====
    class NotificationSystem {
        constructor() {
            this.container = this.createContainer();
            this.notifications = new Map();
        }

        createContainer() {
            const container = document.createElement('div');
            container.className = 'notification-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
            return container;
        }

        show(message, type = 'info', duration = CONFIG.notification.duration) {
            const id = Utils.generateId();
            const notification = this.createNotification(id, message, type);
            
            this.container.appendChild(notification);
            this.notifications.set(id, notification);

            // 动画显示
            requestAnimationFrame(() => {
                notification.classList.add('show');
            });

            // 自动隐藏
            if (duration > 0) {
                setTimeout(() => {
                    this.hide(id);
                }, duration);
            }

            return id;
        }

        createNotification(id, message, type) {
            const notification = document.createElement('div');
            notification.className = `notification glass-card mb-2 p-3 text-white animate__animated animate__fadeInRight`;
            notification.dataset.id = id;

            const icons = {
                success: 'fas fa-check-circle text-success',
                error: 'fas fa-exclamation-circle text-danger',
                warning: 'fas fa-exclamation-triangle text-warning',
                info: 'fas fa-info-circle text-info'
            };

            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="${icons[type] || icons.info} me-2"></i>
                    <span class="flex-grow-1">${message}</span>
                    <button type="button" class="btn-close btn-close-white ms-2" onclick="window.NotificationSystem.hide('${id}')"></button>
                </div>
            `;

            return notification;
        }

        hide(id) {
            const notification = this.notifications.get(id);
            if (notification) {
                notification.classList.remove('animate__fadeInRight');
                notification.classList.add('animate__fadeOutRight');
                
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                    this.notifications.delete(id);
                }, 300);
            }
        }

        success(message, duration) {
            return this.show(message, 'success', duration);
        }

        error(message, duration) {
            return this.show(message, 'error', duration);
        }

        warning(message, duration) {
            return this.show(message, 'warning', duration);
        }

        info(message, duration) {
            return this.show(message, 'info', duration);
        }
    }

    // ===== 主题管理器 =====
    class ThemeManager {
        constructor() {
            this.currentTheme = this.getStoredTheme() || CONFIG.theme.defaultTheme;
            this.init();
        }

        init() {
            this.applyTheme(this.currentTheme);
            this.bindEvents();
            this.setupAutoSwitch();
        }

        getStoredTheme() {
            try {
                return localStorage.getItem(CONFIG.theme.storageKey);
            } catch (e) {
                console.warn('无法访问 localStorage:', e);
                return null;
            }
        }

        setStoredTheme(theme) {
            try {
                localStorage.setItem(CONFIG.theme.storageKey, theme);
            } catch (e) {
                console.warn('无法保存主题设置:', e);
            }
        }

        applyTheme(theme) {
            document.documentElement.setAttribute('data-bs-theme', theme);
            this.currentTheme = theme;
            this.setStoredTheme(theme);
            this.updateThemeButtons();
            
            // 触发主题变更事件
            window.dispatchEvent(new CustomEvent('themeChanged', {
                detail: { theme }
            }));
        }

        updateThemeButtons() {
            const buttons = document.querySelectorAll('[data-bs-theme-value]');
            buttons.forEach(button => {
                const isActive = button.dataset.bsThemeValue === this.currentTheme;
                button.classList.toggle('active', isActive);
            });
        }

        bindEvents() {
            document.addEventListener('click', (e) => {
                const themeButton = e.target.closest('[data-bs-theme-value]');
                if (themeButton) {
                    e.preventDefault();
                    const theme = themeButton.dataset.bsThemeValue;
                    this.applyTheme(theme);
                }
            });
        }

        setupAutoSwitch() {
            if (this.currentTheme === 'auto') {
                this.updateAutoTheme();
                setInterval(() => {
                    this.updateAutoTheme();
                }, 60000); // 每分钟检查一次
            }
        }

        updateAutoTheme() {
            const hour = new Date().getHours();
            const { light, dark } = CONFIG.theme.autoSwitchTime;
            const shouldBeDark = hour < light || hour >= dark;
            const autoTheme = shouldBeDark ? 'dark' : 'light';
            
            if (document.documentElement.getAttribute('data-bs-theme') !== autoTheme) {
                document.documentElement.setAttribute('data-bs-theme', autoTheme);
            }
        }
    }

    // ===== 页面加载管理器 =====
    class LoadingManager {
        constructor() {
            this.loadingScreen = document.getElementById('loading-screen');
            this.isLoading = false;
        }

        show(message = '加载中...') {
            if (this.loadingScreen) {
                this.loadingScreen.querySelector('h5').textContent = message;
                this.loadingScreen.style.display = 'flex';
                this.loadingScreen.style.opacity = '1';
                this.isLoading = true;
            }
        }

        hide() {
            if (this.loadingScreen && this.isLoading) {
                this.loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    this.loadingScreen.style.display = 'none';
                    this.isLoading = false;
                }, 300);
            }
        }
    }

    // ===== 全局实例化 =====
    const httpClient = new HttpClient();
    const notificationSystem = new NotificationSystem();
    const themeManager = new ThemeManager();
    const loadingManager = new LoadingManager();

    // ===== 全局对象暴露 =====
    window.EnterpriseNeon = {
        Utils,
        HttpClient,
        NotificationSystem: notificationSystem,
        ThemeManager: themeManager,
        LoadingManager: loadingManager,
        CONFIG
    };

    // 兼容性别名
    window.showToast = (message, type) => notificationSystem.show(message, type);
    window.showLoading = (message) => loadingManager.show(message);
    window.hideLoading = () => loadingManager.hide();

    // ===== DOM 加载完成后初始化 =====
    document.addEventListener('DOMContentLoaded', function() {
        // 隐藏加载屏幕
        setTimeout(() => {
            loadingManager.hide();
        }, 500);

        // 初始化返回顶部按钮
        initBackToTop();

        // 初始化导航栏滚动效果
        initNavbarScroll();

        // 初始化表单增强
        initFormEnhancements();

        console.log('Enterprise Neon Theme initialized successfully');
    });

    // ===== 返回顶部功能 =====
    function initBackToTop() {
        const backToTopBtn = document.getElementById('back-to-top');
        if (!backToTopBtn) return;

        const toggleVisibility = Utils.throttle(() => {
            if (window.pageYOffset > 300) {
                backToTopBtn.style.display = 'block';
                backToTopBtn.style.opacity = '1';
            } else {
                backToTopBtn.style.opacity = '0';
                setTimeout(() => {
                    if (window.pageYOffset <= 300) {
                        backToTopBtn.style.display = 'none';
                    }
                }, 300);
            }
        }, 100);

        window.addEventListener('scroll', toggleVisibility);

        backToTopBtn.addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }

    // ===== 导航栏滚动效果 =====
    function initNavbarScroll() {
        const navbar = document.querySelector('.navbar-glass');
        if (!navbar) return;

        const handleScroll = Utils.throttle(() => {
            if (window.pageYOffset > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }, 100);

        window.addEventListener('scroll', handleScroll);
    }

    // ===== 表单增强 =====
    function initFormEnhancements() {
        // 自动聚焦第一个输入框
        const firstInput = document.querySelector('form input:not([type="hidden"]):not([disabled])');
        if (firstInput) {
            firstInput.focus();
        }

        // 表单提交增强
        document.addEventListener('submit', function(e) {
            const form = e.target;
            if (form.tagName === 'FORM') {
                const submitBtn = form.querySelector('[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<span class="loading-spinner me-2"></span>提交中...';
                    
                    // 5秒后重新启用按钮（防止卡死）
                    setTimeout(() => {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = submitBtn.dataset.originalText || '提交';
                    }, 5000);
                }
            }
        });
    }

})();
