/**
 * Neon Theme Scripts - 运营级霓虹灯效果脚本
 * Version: 2.0.0
 * Author: Enterprise Development Team
 */

(function() {
    'use strict';

    // ===== 霓虹灯效果管理器 =====
    class NeonEffectsManager {
        constructor() {
            this.effects = new Map();
            this.isInitialized = false;
            this.performanceMode = this.detectPerformanceMode();
            this.init();
        }

        // 检测性能模式
        detectPerformanceMode() {
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            const isLowEnd = connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g');
            const isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
            return isLowEnd || isReducedMotion ? 'low' : 'high';
        }

        // 初始化霓虹灯效果
        init() {
            if (this.isInitialized) return;

            this.setupThemeManager();
            this.setupNeonEffects();
            this.setupInteractiveElements();
            this.setupPerformanceOptimization();

            this.isInitialized = true;
            console.log('🌟 Neon Effects Manager initialized');
        }

        // 主题管理器
        setupThemeManager() {
            const themeItems = document.querySelectorAll('[data-bs-theme-value]');
            const savedTheme = localStorage.getItem('neon_theme') || 'auto';

            // 应用保存的主题
            this.applyTheme(savedTheme);

            // 主题切换事件
            themeItems.forEach(item => {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    const themeValue = item.dataset.bsThemeValue;
                    this.applyTheme(themeValue);
                    this.saveTheme(themeValue);
                    this.updateActiveIndicator(themeItems, item);
                });
            });

            // 自动主题检测
            if (savedTheme === 'auto') {
                this.setupAutoTheme();
            }
        }

        // 应用主题
        applyTheme(theme) {
            document.documentElement.setAttribute('data-bs-theme', theme);
            document.body.classList.toggle('neon-dark-mode', theme === 'dark');

            // 触发主题变更事件
            window.dispatchEvent(new CustomEvent('neonThemeChanged', {
                detail: { theme }
            }));
        }

        // 保存主题
        saveTheme(theme) {
            localStorage.setItem('neon_theme', theme);
        }

        // 更新活动指示符
        updateActiveIndicator(items, activeItem) {
            items.forEach(item => item.classList.remove('active'));
            activeItem.classList.add('active');
        }

        // 自动主题设置
        setupAutoTheme() {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

            const updateTheme = () => {
                const theme = mediaQuery.matches ? 'dark' : 'light';
                this.applyTheme(theme);
            };

            mediaQuery.addEventListener('change', updateTheme);
            updateTheme();
        }

        // 设置霓虹灯效果
        setupNeonEffects() {
            this.initTextEffects();
            this.initButtonEffects();
            this.initCardEffects();
            this.initInputEffects();
        }

        // 文字效果初始化
        initTextEffects() {
            const neonTexts = document.querySelectorAll('.neon-text, [class*="neon-"]');

            neonTexts.forEach(element => {
                // 添加基础类
                element.classList.add('neon-text-base');

                // 鼠标悬停增强效果
                element.addEventListener('mouseenter', () => {
                    if (this.performanceMode === 'high') {
                        element.style.transform = 'scale(1.05)';
                        element.style.filter = 'brightness(1.2)';
                    }
                });

                element.addEventListener('mouseleave', () => {
                    element.style.transform = '';
                    element.style.filter = '';
                });
            });
        }

        // 按钮效果初始化
        initButtonEffects() {
            const neonButtons = document.querySelectorAll('.neon-button');

            neonButtons.forEach(button => {
                // 点击波纹效果
                button.addEventListener('click', (e) => {
                    if (this.performanceMode === 'low') return;

                    const ripple = document.createElement('span');
                    const rect = button.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: neon-ripple 0.6s ease-out;
                        pointer-events: none;
                        z-index: 1;
                    `;

                    button.style.position = 'relative';
                    button.style.overflow = 'hidden';
                    button.appendChild(ripple);

                    setTimeout(() => ripple.remove(), 600);
                });
            });
        }

        // 卡片效果初始化
        initCardEffects() {
            const neonCards = document.querySelectorAll('.neon-card');

            neonCards.forEach(card => {
                // 3D倾斜效果
                card.addEventListener('mousemove', (e) => {
                    if (this.performanceMode === 'low') return;

                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    const rotateX = (y - centerY) / 10;
                    const rotateY = (centerX - x) / 10;

                    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.02, 1.02, 1.02)`;
                });

                card.addEventListener('mouseleave', () => {
                    card.style.transform = '';
                });
            });
        }

        // 输入框效果初始化
        initInputEffects() {
            const neonInputs = document.querySelectorAll('.neon-input');

            neonInputs.forEach(input => {
                // 聚焦动画
                input.addEventListener('focus', () => {
                    input.style.boxShadow = '0 0 20px currentColor, 0 0 40px currentColor';
                });

                input.addEventListener('blur', () => {
                    input.style.boxShadow = '';
                });

                // 输入验证视觉反馈
                input.addEventListener('input', () => {
                    const isValid = input.checkValidity();
                    input.style.borderColor = isValid ? 'var(--neon-lime-green)' : 'var(--neon-crimson)';
                });
            });
        }

        // 交互元素设置
        setupInteractiveElements() {
            // 滚动视差效果
            this.setupScrollEffects();

            // 鼠标跟踪效果
            if (this.performanceMode === 'high') {
                this.setupMouseTracker();
            }
        }

        // 滚动效果
        setupScrollEffects() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('neon-visible');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('[class*="neon-"]').forEach(el => {
                observer.observe(el);
            });
        }

        // 鼠标跟踪器
        setupMouseTracker() {
            const cursor = document.createElement('div');
            cursor.className = 'neon-cursor';
            cursor.style.cssText = `
                position: fixed;
                width: 20px;
                height: 20px;
                background: radial-gradient(circle, rgba(0,212,255,0.8) 0%, transparent 70%);
                border-radius: 50%;
                pointer-events: none;
                z-index: 9999;
                mix-blend-mode: screen;
                transition: transform 0.1s ease;
            `;
            document.body.appendChild(cursor);

            document.addEventListener('mousemove', (e) => {
                cursor.style.left = e.clientX - 10 + 'px';
                cursor.style.top = e.clientY - 10 + 'px';
            });

            // 悬停在霓虹元素上时放大光标
            document.querySelectorAll('[class*="neon-"]').forEach(el => {
                el.addEventListener('mouseenter', () => {
                    cursor.style.transform = 'scale(2)';
                });
                el.addEventListener('mouseleave', () => {
                    cursor.style.transform = 'scale(1)';
                });
            });
        }

        // 性能优化
        setupPerformanceOptimization() {
            // 防抖函数
            const debounce = (func, wait) => {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            };

            // 窗口大小变化时重新计算效果
            window.addEventListener('resize', debounce(() => {
                this.recalculateEffects();
            }, 250));

            // 页面可见性变化时暂停/恢复动画
            document.addEventListener('visibilitychange', () => {
                const isHidden = document.hidden;
                document.querySelectorAll('[class*="neon-"]').forEach(el => {
                    el.style.animationPlayState = isHidden ? 'paused' : 'running';
                });
            });
        }

        // 重新计算效果
        recalculateEffects() {
            // 重新检测性能模式
            this.performanceMode = this.detectPerformanceMode();

            // 根据性能模式调整效果
            if (this.performanceMode === 'low') {
                document.body.classList.add('neon-performance-mode');
            } else {
                document.body.classList.remove('neon-performance-mode');
            }
        }

        // 公共API方法
        addNeonEffect(element, effectType, options = {}) {
            if (!element) return;

            const effectId = Date.now() + Math.random();
            this.effects.set(effectId, { element, effectType, options });

            element.classList.add(`neon-${effectType}`);

            return effectId;
        }

        removeNeonEffect(effectId) {
            const effect = this.effects.get(effectId);
            if (effect) {
                effect.element.classList.remove(`neon-${effect.effectType}`);
                this.effects.delete(effectId);
            }
        }

        toggleEffect(selector, effectClass) {
            document.querySelectorAll(selector).forEach(el => {
                el.classList.toggle(effectClass);
            });
        }
    }

    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes neon-ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .neon-visible {
            animation: neon-fade-in 0.8s ease-out;
        }

        @keyframes neon-fade-in {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .neon-performance-mode * {
            animation-duration: 0.1s !important;
            transition-duration: 0.1s !important;
        }
    `;
    document.head.appendChild(style);

    // 初始化管理器
    let neonManager;

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            neonManager = new NeonEffectsManager();
        });
    } else {
        neonManager = new NeonEffectsManager();
    }

    // 全局API
    window.NeonEffects = {
        manager: () => neonManager,
        add: (element, effect, options) => neonManager?.addNeonEffect(element, effect, options),
        remove: (effectId) => neonManager?.removeNeonEffect(effectId),
        toggle: (selector, effectClass) => neonManager?.toggleEffect(selector, effectClass)
    };

})();

// 兼容旧版本的主题切换代码
document.addEventListener('DOMContentLoaded', function() {
    // 监听主题切换事件
    const themeItems = document.querySelectorAll('[data-bs-theme-value]');
    themeItems.forEach(item => {
        item.addEventListener('click', function() {
            const themeValue = this.dataset.bsThemeValue;
            document.documentElement.setAttribute('data-bs-theme', themeValue);
            localStorage.setItem('theme', themeValue);
            
            // 更新活动指示符
            themeItems.forEach(i => i.classList.remove('active'));
            this.classList.add('active');
        });
    });
});
