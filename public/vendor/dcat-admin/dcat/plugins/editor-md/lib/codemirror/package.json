{"name": "codemirror", "version": "5.0.0", "main": "lib/codemirror.js", "description": "In-browser code editing made bearable", "licenses": [{"type": "MIT", "url": "http://codemirror.net/LICENSE"}], "directories": {"lib": "./lib"}, "scripts": {"test": "node ./test/run.js"}, "devDependencies": {"node-static": "0.6.0", "phantomjs": "1.9.2-5", "blint": ">=0.1.1"}, "bugs": "http://github.com/codemirror/CodeMirror/issues", "keywords": ["JavaScript", "CodeMirror", "Editor"], "homepage": "http://codemirror.net", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://marijnhaverbeke.nl"}], "repository": {"type": "git", "url": "https://github.com/codemirror/CodeMirror.git"}}