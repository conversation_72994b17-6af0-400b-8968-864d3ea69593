<!doctype html>

<title>CodeMirror: NTriples mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="ntriples.js"></script>
<style type="text/css">
      .CodeMirror {
        border: 1px solid #eee;
      }
    </style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">NTriples</a>
  </ul>
</div>

<article>
<h2>NTriples mode</h2>
<form>
<textarea id="ntriples" name="ntriples">    
<http://Sub1>     <http://pred1>     <http://obj> .
<http://Sub2>     <http://pred2#an2> "literal 1" .
<http://Sub3#an3> <http://pred3>     _:bnode3 .
_:bnode4          <http://pred4>     "literal 2"@lang .
_:bnode5          <http://pred5>     "literal 3"^^<http://type> .
</textarea>
</form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("ntriples"), {});
    </script>
    <p><strong>MIME types defined:</strong> <code>text/n-triples</code>.</p>
  </article>
